// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/utils/CountersUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/StringsUpgradeable.sol";

import "../interfaces//IContractManager.sol";
import "../interfaces/Error.sol";
import "../interfaces/RenewableEnergyTokenStruct.sol";
import "../interfaces/IRenewableEnergyToken.sol";
import "./libraries/RenewableEnergyTokenLib.sol";
import "./libraries/StringUtils.sol";
import "../interfaces/ITransferable.sol";

// TODO: RenewableEnergyToken.solはカスタムコントラクトとして呼び出すため、別フォルダに切り出す(DCPF-15665で対応)

contract RenewableEnergyToken is Initializable, IRenewableEnergyToken {
    ///////////////////////////////////
    // libraries
    ///////////////////////////////////

    using StringsUpgradeable for uint256;
    using CountersUpgradeable for CountersUpgradeable.Counter;
    using RenewableEnergyTokenLib for *;
    using StringUtils for *;

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;
    /** @dev Tokenコントラクトアドレス */
    ITransferable private _token;
    /** @dev tokenId */
    bytes32[] private _tokenIds;
    /** @dev RenewableEnergyTokenData */
    mapping(bytes32 => RenewableEnergyTokenData) private _renewableEnergyTokenData;
    /** @dev アカウントIDごとの所持トークン一覧 */
    mapping(bytes32 => bytes32[]) private _tokenIdsByAccountId;
    /* トークンの有効状態 TODO: 現時点のCoreAPIの観点からは利用しないが、将来的に利用を検討 */
    // bytes32 private _enabled;
    /* トークンの名称 TODO: 現時点のCoreAPIの観点からは利用しないが、将来的に利用を検討 */
    // bytes32 private _tokenName;
    /** @dev 発行可能総数 TODO: 現時点のCoreAPIの観点からは利用しないが、将来的に利用を検討 */
    // uint256 private totalSupply;

    /** @dev 未登録の場合にて返す空の件数 **/
    uint256 private constant _EMPTY_LENGTH = 0;
    /** @dev GET ALL関数の最大取得件数 */
    uint256 private constant _GET_RETOKENS_LIMIT = 1000;
    /** @dev getTokenAllのsignature検証用 **/
    string private constant _GET_RETOKEN_ALL_SIGNATURE = "getRETokensAll";
    /* @dev setTokenAllのsignature検証用 */
    string private constant _SET_RETOKEN_ALL_SIGNATURE = "setRETokensAll";

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     *
     * @param contractManager ContractManagerアドレス
     */
    function initialize(address contractManager, ITransferable token) public initializer {
        _contractManager = IContractManager(contractManager);
        _token = ITransferable(token);
    }

    /**
     * @dev コントラクトバージョン取得。
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev トークンを発行する
     *
     * @param tokenId トークンID
     * @param metadataId メタデータID
     * @param metadataHash メタデータハッシュ
     * @param mintAccountId ミントアカウントID
     * @param ownerAccountId オーナーアカウントID
     * @param isLocked ロック状態
     * @param traceId トレースID
     */
    function mint(
        bytes32 tokenId,
        bytes32 metadataId,
        bytes32 metadataHash,
        bytes32 mintAccountId,
        bytes32 ownerAccountId,
        bool isLocked,
        bytes32 traceId
    ) external override {
        // トークンIDが無効な値でないかを確認
        require(tokenId != 0x00, Error.RV0015_RETOKEN_INVALID_VAL);

        // トークンIDがすでに存在するかどうかを確認
        require(!_renewableEnergyTokenData.exists(tokenId), Error.GE1019_RETOKEN_ID_EXIST);

        _tokenIds.addTokenId(tokenId);
        _tokenIdsByAccountId.addTokenIdToAccountId(ownerAccountId, tokenId);
        _renewableEnergyTokenData.addToken(
            tokenId,
            metadataId,
            metadataHash,
            mintAccountId,
            ownerAccountId,
            isLocked
        );

        emit MintRNToken(
            tokenId,
            metadataId,
            metadataHash,
            mintAccountId,
            ownerAccountId,
            isLocked,
            traceId
        );
    }

    /**
     * @dev トークンを移転する
     *
     * @param fromAccountId 移転元アカウントID
     * @param toAccountId 移転先アカウントID
     * @param tokenId トークンID
     * @param traceId トレースID
     */
    function transfer(
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 tokenId,
        bytes32 traceId
    ) external override {
        // 同じアカウントID同士でトークンを送る場合はエラー
        require(fromAccountId != toAccountId, Error.UE2006_RETOKEN_FROM_TO_ARE_SAME);

        // fromAccountIdが該当トークンを保有しているかどうか確認
        (bool success, string memory err) = this.hasToken(tokenId, fromAccountId);
        require(success, err);

        _renewableEnergyTokenData.transferToken(
            _tokenIdsByAccountId,
            tokenId,
            fromAccountId,
            toAccountId
        );

        // Event用にfromAccountIdが紐づくValidatorのIdを取得する
        (bytes32 validatorId, ) = _contractManager.account().getValidatorIdByAccountId(
            fromAccountId
        );

        emit TransferRNToken(
            validatorId,
            fromAccountId,
            fromAccountId,
            toAccountId,
            tokenId,
            traceId
        );
    }

    /**
     * @dev トークンを移転する(カスタムトランスファー)
     *
     * @param sendAccountId SendAccount
     * @param fromAccountId FromAccount
     * @param toAccountId toAccount
     * @param amount 金額
     * @param miscValue1 カスタムコントラクト用パラメータ1
     * @param miscValue2 カスタムコントラクト用パラメータ2
     * @param traceId トレースID
     */
    function customTransfer(
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        string memory memo,
        bytes32 traceId
    ) external override returns (bool result) {
        require(address(_token) != address(0), "Invalid contract address");
        if (miscValue1 == bytes32("renewable")) {
            // TODO: miscValue2のフォーマット, エラーメッセージはCoreAPIの仕様決定後に確定させる
            require(bytes(miscValue2).length != 0, Error.RV0015_RETOKEN_INVALID_VAL);

            // 同じアカウントID同士でトークンを送る場合はエラー
            require(fromAccountId != toAccountId, Error.UE2006_RETOKEN_FROM_TO_ARE_SAME);

            string[] memory miscValue2Array = miscValue2.slice(",");

            _renewableEnergyTokenData.transferBatchTokens(
                _tokenIdsByAccountId,
                miscValue2Array,
                toAccountId,
                fromAccountId
            );

            return
                _token.customTransfer(
                    sendAccountId,
                    fromAccountId,
                    toAccountId,
                    amount,
                    miscValue1,
                    miscValue2,
                    memo,
                    traceId
                );
        }
        return false;
    }

    /**
     * @dev RenewableEnergyTokenAll情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param renewableEnergytokens renewableEnergytokens
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function restoreRenewableEnergyTokens(
        RenewableEnergyTokenAll[] memory renewableEnergytokens,
        uint256 deadline,
        bytes memory signature
    ) external {
        for (uint256 i = 0; i < renewableEnergytokens.length; i++) {
            this.setRenewableEnergyTokenAll(renewableEnergytokens[i], deadline, signature);
        }
    }

    /**
     * @dev TokenIdsByAccountIdAll情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param tokenIdsByAccountId tokenIdsByAccountId
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function restoreTokenIdsByAccountId(
        TokenIdsByAccountIdAll[] memory tokenIdsByAccountId,
        uint256 deadline,
        bytes memory signature
    ) external {
        for (uint256 i = 0; i < tokenIdsByAccountId.length; i++) {
            this.setTokenIdsByAccountIdAll(tokenIdsByAccountId[i], deadline, signature);
        }
    }

    /**
     * @dev 指定されたtokenIdに紐づくToken情報を登録、もしくは上書きする
     * @param renewableEnergytokens Tokenの情報
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setRenewableEnergyTokenAll(
        RenewableEnergyTokenAll memory renewableEnergytokens,
        uint256 deadline,
        bytes memory signature
    ) external {
        // Admin権限を持つかチェック
        bytes32 hash = keccak256(abi.encode(_SET_RETOKEN_ALL_SIGNATURE, deadline));
        (bool has, string memory _err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(_err).length == 0, _err);
        require(has, Error.RV0001_ACTRL_BAD_ROLE);

        RenewableEnergyTokenLib.setRenewableEnergyTokenAll(
            _renewableEnergyTokenData,
            _tokenIds,
            renewableEnergytokens
        );
    }

    /**
     * @dev 指定されたaccountIdに紐づくTokenIdを登録、もしくは上書きする
     * @param tokenIdByAccountId accountIdに紐づくTokenId
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setTokenIdsByAccountIdAll(
        TokenIdsByAccountIdAll memory tokenIdByAccountId,
        uint256 deadline,
        bytes memory signature
    ) external {
        // Admin権限を持つかチェック
        bytes32 hash = keccak256(abi.encode(_SET_RETOKEN_ALL_SIGNATURE, deadline));
        (bool has, string memory _err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(_err).length == 0, _err);
        require(has, Error.RV0001_ACTRL_BAD_ROLE);

        RenewableEnergyTokenLib.setTokenIdsByAccountIdAll(_tokenIdsByAccountId, tokenIdByAccountId);
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev トークンの移転チェック
     *
     * @param fromAccountId 移転元アカウントID
     * @param toAccountId 移転先アカウントID
     * @param sendAccountId 送信元アカウントID
     * @param miscValue1 miscValue1
     * @param miscValue2 miscValue2
     */
    function checkTransaction(
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 miscValue1,
        string memory miscValue2
    ) external view returns (bool success, string memory err) {
        string[] memory miscValue2Array = miscValue2.slice(",");

        return
            RenewableEnergyTokenLib.checkTransaction(
                address(_contractManager),
                _renewableEnergyTokenData,
                sendAccountId,
                fromAccountId,
                toAccountId,
                miscValue1,
                miscValue2Array
            );
    }

    /**
     * @dev アカウントに紐づくトークンの一覧を取得する
     *
     * @param accountId アカウントID
     * @param offset offset
     * @param limit limit
     * @param sortOrder sortOrder(true: 降順, false: 昇順)
     * @return renewableEnergyTokenList トークンデータ一覧
     */
    function getTokenList(
        bytes32 validatorId,
        bytes32 accountId,
        uint256 offset,
        uint256 limit,
        string memory sortOrder // コントラクト側でソートを行うことは非効率なので利用しない(TODO: Coreと平仄を合わせて修正しパラメータから削除する)
    )
        external
        view
        override
        returns (
            RenewableEnergyTokenListData[] memory renewableEnergyTokenList,
            uint256 totalCount,
            string memory err
        )
    {
        (bool success, string memory errTemp) = _contractManager.validator().hasAccount(
            validatorId,
            accountId
        );
        if (!success) {
            return (new RenewableEnergyTokenListData[](0), 0, errTemp);
        }

        return
            RenewableEnergyTokenLib.getTokenList(
                _renewableEnergyTokenData,
                _tokenIds,
                accountId,
                offset,
                limit
            );
    }

    /**
     * @dev トークンの詳細情報を取得する
     *
     * @param tokenId トークンID
     * @return renewableEnergyTokenData トークンデータ
     */
    function getToken(bytes32 tokenId)
        external
        view
        override
        returns (RenewableEnergyTokenData memory renewableEnergyTokenData, string memory err)
    {
        if (tokenId == 0x00) {
            return (renewableEnergyTokenData, Error.RV0015_RETOKEN_INVALID_VAL);
        }
        return _renewableEnergyTokenData.getToken(tokenId);
    }

    /**
     * @dev 引数のアカウントIDが、引数のトークンIDのNFTを所有しているか確認する
     *
     * @param tokenId トークンID
     * @param accountId アカウントID
     */
    function hasToken(bytes32 tokenId, bytes32 accountId)
        external
        view
        returns (bool success, string memory err)
    {
        // Tokenの存在確認
        if (!_renewableEnergyTokenData.exists(tokenId)) {
            return (false, Error.GE0112_RETOKEN_NOT_EXIST);
        }
        // Tokenの所有者確認
        if (_renewableEnergyTokenData[tokenId].ownerAccountId != accountId) {
            return (false, Error.GA0028_RETOKEN_NOT_OWNER);
        }

        return (true, "");
    }

    /**
     * @dev Tokenの数を返却する。
     *
     * @param count tokenの数
     */
    function getTokenCount() external view returns (uint256 count) {
        return _tokenIds.length;
    }

    /**
     * @dev limitとoffsetで指定したRenewableEnergyTokensを一括取得する
     *
     * @param offset オフセット
     * @param limit 取得件数
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     * @return renewableEnergyTokenAll 全RenewableEnergyTokenの情報
     * @return totalCount 取得件数
     * @return err エラーメッセージ
     */
    function backupRenewableEnergyTokens(
        uint256 offset,
        uint256 limit,
        uint256 deadline,
        bytes memory signature
    )
        external
        view
        returns (
            RenewableEnergyTokenAll[] memory renewableEnergyTokenAll,
            uint256 totalCount,
            string memory err
        )
    {
        // Admin権限を持つかチェック
        bytes32 hash = keccak256(abi.encode(_GET_RETOKEN_ALL_SIGNATURE, deadline));
        (bool has, string memory _err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );

        if (!has) {
            err = (bytes(_err).length != 0) ? _err : Error.RV0001_ACTRL_BAD_ROLE;
            return (renewableEnergyTokenAll, 0, err);
        }

        uint256 count = this.getTokenCount();

        if (limit == 0 || count == 0) {
            return (renewableEnergyTokenAll, _EMPTY_LENGTH, "");
        }
        if (limit > _GET_RETOKENS_LIMIT) {
            return (renewableEnergyTokenAll, _EMPTY_LENGTH, Error.UE0113_RETOKEN_TOO_LARGE_LIMIT);
        }

        if (offset >= count) {
            return (
                renewableEnergyTokenAll,
                _EMPTY_LENGTH,
                Error.UE0114_RETOKEN_OFFSET_OUT_OF_INDEX
            );
        }

        uint256 size = (count >= offset + limit) ? limit : count - offset;
        renewableEnergyTokenAll = new RenewableEnergyTokenAll[](size);

        for (uint256 i = 0; i < size; i++) {
            renewableEnergyTokenAll[i] = this.getRenewableEnergyTokenAll(i + offset);
        }

        return (renewableEnergyTokenAll, count, "");
    }

    /**
     * @dev limitとoffsetで指定したTokenIdsByAccountIdsを一括取得する
     *
     * @param offset オフセット
     * @param limit 取得件数
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     * @return tokenIdsByAccountIdAll 全TokenIdsByAccountIdの情報
     * @return totalCount 取得件数
     * @return err エラーメッセージ
     */
    function backupTokenIdsByAccountIds(
        uint256 offset,
        uint256 limit,
        uint256 deadline,
        bytes memory signature
    )
        external
        view
        returns (
            TokenIdsByAccountIdAll[] memory tokenIdsByAccountIdAll,
            uint256 totalCount,
            string memory err
        )
    {
        // Admin権限を持つかチェック
        bytes32 hash = keccak256(abi.encode(_GET_RETOKEN_ALL_SIGNATURE, deadline));
        (bool has, string memory _err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );

        if (!has) {
            err = (bytes(_err).length != 0) ? _err : Error.RV0001_ACTRL_BAD_ROLE;
            return (tokenIdsByAccountIdAll, 0, err);
        }

        uint256 count = _contractManager.account().getAccountCount();

        if (limit == 0 || count == 0) {
            return (tokenIdsByAccountIdAll, _EMPTY_LENGTH, "");
        }
        if (limit > _GET_RETOKENS_LIMIT) {
            return (tokenIdsByAccountIdAll, _EMPTY_LENGTH, Error.UE0113_RETOKEN_TOO_LARGE_LIMIT);
        }

        if (offset >= count) {
            return (
                tokenIdsByAccountIdAll,
                _EMPTY_LENGTH,
                Error.UE0114_RETOKEN_OFFSET_OUT_OF_INDEX
            );
        }

        uint256 size = (count >= offset + limit) ? limit : count - offset;
        tokenIdsByAccountIdAll = new TokenIdsByAccountIdAll[](size);

        for (uint256 i = 0; i < size; i++) {
            (bytes32 accountId, ) = _contractManager.account().getAccountId(i + offset);
            tokenIdsByAccountIdAll[i] = this.getTokenIdsByAccountIdAll(accountId);
        }

        return (tokenIdsByAccountIdAll, count, "");
    }

    /**
     * @dev RenewableEnergyToken全情報取得
     *      既に登録されているRenewableEnergyToken全情報取得を取得する
     *
     * @param index オフセット
     * @return renewableEnergyToken 全Tokenの情報
     */
    function getRenewableEnergyTokenAll(uint256 index)
        external
        view
        returns (RenewableEnergyTokenAll memory renewableEnergyToken)
    {
        return
            RenewableEnergyTokenLib.getRenewableEnergyTokenAll(
                _renewableEnergyTokenData,
                _tokenIds,
                index
            );
    }

    /**
     * @dev 指定されたaccountIdに紐づくTokenIdを取得
     * @param accountId accountId
     */
    function getTokenIdsByAccountIdAll(bytes32 accountId)
        external
        view
        returns (TokenIdsByAccountIdAll memory tokenIdsByAccountId)
    {
        return RenewableEnergyTokenLib.getTokenIdsByAccountIdAll(_tokenIdsByAccountId, accountId);
    }
}
