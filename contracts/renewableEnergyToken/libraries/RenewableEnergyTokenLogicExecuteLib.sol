// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "@openzeppelin/contracts-upgradeable/utils/math/SafeMathUpgradeable.sol";
import "../../interfaces/IContractManager.sol";
import "../../interfaces/IRenewableEnergyTokenStorage.sol";
import "../../interfaces/Struct.sol";
import "../../interfaces/Error.sol";
import "../../renewableEnergyToken/libraries/StringUtils.sol";
import "../../interfaces/ITransferable.sol";

/**
 * @dev RenewableEnergyTokenLogicExecuteLibライブラリ
 *      RenewableEnergyTokenの実行関数を実装するヘルパーライブラリ
 */
library RenewableEnergyTokenLogicExecuteLib {
    using SafeMathUpgradeable for uint256;

    /**
     * @dev トークンを発行する
     *
     * @param tokenId トークンID
     * @param metadataId メタデータID
     * @param metadataHash メタデータハッシュ
     * @param mintAccountId ミントアカウントID
     * @param ownerAccountId オーナーアカウントID
     * @param isLocked ロック状態
     */
    function executeMint(
        IRenewableEnergyTokenStorage tokenStorage,
        bytes32 tokenId,
        bytes32 metadataId,
        bytes32 metadataHash,
        bytes32 mintAccountId,
        bytes32 ownerAccountId,
        bool isLocked
    ) external {
        tokenStorage.addTokenId(tokenId);
        tokenStorage.addTokenIdToAccountId(ownerAccountId, tokenId);
        tokenStorage.addToken(
            tokenId,
            metadataId,
            metadataHash,
            mintAccountId,
            ownerAccountId,
            isLocked
        );
    }

    /**
     * @dev トークンを移転する
     *
     * @param fromAccountId 移転元アカウントID
     * @param toAccountId 移転先アカウントID
     * @param tokenId トークンID
     * @param tokenStorage RenewableEnergyTokenStorageコントラクト
     */
    function executeTransfer(
        IRenewableEnergyTokenStorage tokenStorage,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 tokenId
    ) external {
        // NFTがアクティブでない、またはロック状態である場合はエラー
        RenewableEnergyTokenData memory data = tokenStorage.getTokenById(tokenId);
        require(data.tokenStatus == TokenStatus.Active, Error.GE2014_RETOKEN_NOT_ACTIVE);
        require(!data.isLocked, Error.GE2015_RETOKEN_IS_LOCKED);
        require(data.ownerAccountId == fromAccountId, Error.GA0028_RETOKEN_NOT_OWNER);

        // 移転元アカウントの該当トークン所有情報を削除する
        tokenStorage.removeTokenIdFromAccountId(fromAccountId, tokenId);
        tokenStorage.setNewAccountIdForToken(fromAccountId, toAccountId, tokenId);
        tokenStorage.addTokenIdToAccountId(toAccountId, tokenId);
    }

    /**
     * @dev トークンを移転する
     * @param tokenIds キーとなるトークンID
     * @param sendAccountId SendAccount
     * @param fromAccountId FromAccount
     * @param toAccountId toAccount
     * @param amount 金額
     * @param miscValue1 カスタムコントラクト用パラメータ1
     * @param miscValue2 カスタムコントラクト用パラメータ2
     * @param memo メモ
     * @param traceId トレースID
     */
    function executeTransferBatchTokens(
        IRenewableEnergyTokenStorage tokenStorage,
        ITransferable token,
        string[] memory tokenIds,
        bytes32 sendAccountId,
        bytes32 toAccountId,
        bytes32 fromAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        string memory memo,
        bytes32 traceId
    ) external returns (bool) {
        for (uint256 i; i < tokenIds.length; i++) {
            bytes32 tokenId = StringUtils.stringToBytes32(tokenIds[i]);
            // NFTがアクティブでない、またはロック状態である場合はエラー
            RenewableEnergyTokenData memory data = tokenStorage.getTokenById(tokenId);
            require(data.tokenStatus == TokenStatus.Active, Error.GE0112_RETOKEN_NOT_EXIST);
            require(!data.isLocked, Error.GE2015_RETOKEN_IS_LOCKED);
            require(data.ownerAccountId == fromAccountId, Error.GA0028_RETOKEN_NOT_OWNER);

            // 移転元アカウントの該当トークン所有情報を削除する
            tokenStorage.removeTokenIdFromAccountId(fromAccountId, tokenId);
            tokenStorage.setNewAccountIdForToken(fromAccountId, toAccountId, tokenId);
            tokenStorage.addTokenIdToAccountId(toAccountId, tokenId);
        }
        return
            token.customTransfer(
                sendAccountId,
                toAccountId,
                fromAccountId,
                amount,
                miscValue1,
                miscValue2,
                memo,
                traceId
            );
    }

    /**
     * @dev RenewableEnergyTokenAll情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param renewableEnergytokens バックアップから復元するトークン情報リスト
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function executeRestoreRenewableEnergyTokens(
        IRenewableEnergyTokenStorage tokenStorage,
        RenewableEnergyTokenAll[] memory renewableEnergytokens,
        uint256 deadline,
        bytes memory signature
    ) external {
        for (uint256 i = 0; i < renewableEnergytokens.length; i++) {
            tokenStorage.setRenewableEnergyTokenAll(renewableEnergytokens[i], deadline, signature);
        }
    }

    /**
     * @dev TokenIdsByAccountIdAll情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param tokenIdsByAccountId バックアップから復元するアカウントとトークンIDの対応リスト
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function executeRestoreTokenIdsByAccountId(
        IRenewableEnergyTokenStorage tokenStorage,
        TokenIdsByAccountIdAll[] memory tokenIdsByAccountId,
        uint256 deadline,
        bytes memory signature
    ) external {
        for (uint256 i = 0; i < tokenIdsByAccountId.length; i++) {
            tokenStorage.setTokenIdsByAccountIdAll(tokenIdsByAccountId[i], deadline, signature);
        }
    }
}
