// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../../interfaces/IContractManager.sol";
import "../../interfaces/IRenewableEnergyTokenStorage.sol";
import "../../interfaces/Struct.sol";
import "../../interfaces/RenewableEnergyTokenStruct.sol";
import "../../interfaces/Error.sol";
import "../../interfaces/ITransferable.sol";
import "../../renewableEnergyToken/libraries/StringUtils.sol";

/**
 * @dev TokenLogicCallLibライブラリ
 *      Tokenのview/pure関数を実装するヘルパーライブラリ
 *      ブロックチェーンデータの変更は行わない
 */
library RenewableEnergyTokenLogicCallLib {
    /** @dev 未登録の場合にて返す空の件数 **/
    uint256 private constant EMPTY_LENGTH = 0;

    uint256 private constant MAX_LIMIT = 100;

    /** @dev GET ALL関数の最大取得件数 */
    uint256 private constant GET_RETOKENS_LIMIT = 1000;

    /** @dev getTokenAllのsignature検証用 **/
    string private constant GET_RETOKEN_ALL_SIGNATURE = "getRETokensAll";

    /**
     * @dev トークン発行の妥当性を検証する (validate mint)
     * @param tokenId トークンID
     */
    function mintIsValid(IRenewableEnergyTokenStorage tokenStorage, bytes32 tokenId) external view {
        // トークンIDが無効な値でないかを確認
        require(tokenId != 0x00, Error.RV0015_RETOKEN_INVALID_VAL);
        // トークンIDがすでに存在するかどうかを確認
        require(
            !(tokenStorage.getTokenById(tokenId).tokenStatus != TokenStatus.Empty),
            Error.GE1019_RETOKEN_ID_EXIST
        );
    }

    /**
     * @dev 引数のアカウントIDが、引数のトークンIDのNFTを所有しているか確認する
     *
     * @param tokenId トークンID
     * @param accountId アカウントID
     */
    function hasToken(
        IRenewableEnergyTokenStorage tokenStorage,
        bytes32 tokenId,
        bytes32 accountId
    ) internal view returns (bool success, string memory err) {
        // Tokenの存在確認
        if (!(tokenStorage.getTokenById(tokenId).tokenStatus != TokenStatus.Empty)) {
            return (false, Error.GE0112_RETOKEN_NOT_EXIST);
        }
        // Tokenの所有者確認
        if (tokenStorage.getTokenById(tokenId).ownerAccountId != accountId) {
            return (false, Error.GA0028_RETOKEN_NOT_OWNER);
        }

        return (true, "");
    }

    /**
     * @dev トークン移転の妥当性を検証する (validate transfer)
     *
     * @param fromAccountId 移転元アカウントID
     * @param toAccountId 移転先アカウントID
     * @param tokenId トークンID
     */
    function transferIsValid(
        IRenewableEnergyTokenStorage _renewableEnergyTokenStorage,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 tokenId
    ) external view {
        // 同じアカウントID同士でトークンを送る場合はエラー
        require(fromAccountId != toAccountId, Error.UE2006_RETOKEN_FROM_TO_ARE_SAME);

        // fromAccountIdが該当トークンを保有しているかどうか確認
        (bool success, string memory err) = RenewableEnergyTokenLogicCallLib.hasToken(
            _renewableEnergyTokenStorage,
            tokenId,
            fromAccountId
        );
        require(success, err);
    }

    /**
     * @dev カスタムトランスファーの妥当性を検証する
     * @param fromAccountId FromAccount
     * @param toAccountId toAccount
     * @param miscValue1 カスタムコントラクト用パラメータ1
     * @param miscValue2 カスタムコントラクト用パラメータ2
     */
    function customTransferIsValid(
        ITransferable _token,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 miscValue1,
        string memory miscValue2
    ) external pure {
        require(address(_token) != address(0), "Invalid contract address");
        if (miscValue1 == bytes32("renewable")) {
            // TODO: miscValue2のフォーマット, エラーメッセージはCoreAPIの仕様決定後に確定させる
            require(bytes(miscValue2).length != 0, Error.RV0015_RETOKEN_INVALID_VAL);

            // 同じアカウントID同士でトークンを送る場合はエラー
            require(fromAccountId != toAccountId, Error.UE2006_RETOKEN_FROM_TO_ARE_SAME);
        }
    }

    /**
     * @dev renewableEnergyTokenDataMappingのマッピング内から、指定されたaccountIdがownerAccountId、mintAccountIdとなっているトークンのリストを取得する
     * @param accountId 取得対象のアカウントID
     * @param offset 取得開始位置
     * @param limit 取得件数
     * @return tokenDataList 取得したトークンのリスト
     * @return totalCount 取得対象のトークン数
     * @return err エラーメッセージ
     */
    function getTokenList(
        IRenewableEnergyTokenStorage tokenStorage,
        IContractManager contractManager,
        bytes32 validatorId,
        bytes32 accountId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        returns (
            RenewableEnergyTokenListData[] memory tokenDataList,
            uint256 totalCount,
            string memory err
        )
    {
        (bool success, string memory errTemp) = contractManager.validator().hasAccount(
            validatorId,
            accountId
        );
        if (!success) {
            return (new RenewableEnergyTokenListData[](0), 0, errTemp);
        }

        // 動的配列をreturnすることが言語使用上できないため、最初に返却対象の配列のサイズを取得する = tokenIdsを総なめして、accountIdがownerAccountIdまたはmintAccountIdとなっているトークンの数をカウントする
        uint256 tokenCount = getTokenCountForGetTokenListFunction(tokenStorage, accountId);

        // キーの長さがオフセットより小さい、またはリミットが0の場合は、空の配列を返す
        if (limit == 0 || tokenCount == 0) {
            return (tokenDataList, EMPTY_LENGTH, "");
        }
        if (limit > MAX_LIMIT) {
            return (tokenDataList, EMPTY_LENGTH, Error.UE0113_RETOKEN_TOO_LARGE_LIMIT);
        }
        if (offset >= tokenCount) {
            return (tokenDataList, tokenCount, Error.UE0114_RETOKEN_OFFSET_OUT_OF_INDEX);
        }

        tokenDataList = buildTokenDataListForGetTokenListFunction(
            tokenStorage,
            accountId,
            offset,
            limit,
            tokenCount
        );

        return (tokenDataList, tokenCount, "");
    }

    /**
     * @dev GetTokenList関数のTokenCountを取得する
     * @param accountId 取得対象のアカウントID
     * @return tokenCount 取得対象のトークン数
     */
    function getTokenCountForGetTokenListFunction(
        IRenewableEnergyTokenStorage tokenStorage,
        bytes32 accountId
    ) internal view returns (uint256 tokenCount) {
        tokenCount = 0;
        for (uint256 i = 0; i < tokenStorage.getTokenCount(); i++) {
            if (
                tokenStorage.getTokenById(tokenStorage.getTokenIdsByIndex(i)).ownerAccountId ==
                accountId ||
                tokenStorage.getTokenById(tokenStorage.getTokenIdsByIndex(i)).mintAccountId ==
                accountId
            ) {
                tokenCount++;
            }
        }
        return tokenCount;
    }

    /**
     * @dev GetTokenList関数用のTokenDataListを構築する
     * @param accountId 取得対象のアカウントID
     * @param offset 取得開始位置
     * @param limit 取得件数
     * @param tokenCount 取得対象のトークン数
     * @return tokenDataList 取得したトークンのリスト
     */
    function buildTokenDataListForGetTokenListFunction(
        IRenewableEnergyTokenStorage tokenStorage,
        bytes32 accountId,
        uint256 offset,
        uint256 limit,
        uint256 tokenCount
    ) internal view returns (RenewableEnergyTokenListData[] memory tokenDataList) {
        uint256 size = (tokenCount >= offset + limit) ? limit : tokenCount - offset;
        // カウント数に基づいて、tokenIdListのサイズを確保し、再度tokenIdsを総なめして、accountIdがownerAccountIdまたはmintAccountIdとなっているトークンのIDをtokenIdListに格納する
        bytes32[] memory tokenIdList = new bytes32[](tokenCount);
        uint256 tokenIndex = 0;
        for (uint256 i = 0; i < tokenStorage.getTokenCount(); i++) {
            if (
                tokenStorage.getTokenById(tokenStorage.getTokenIdsByIndex(i)).ownerAccountId ==
                accountId ||
                tokenStorage.getTokenById(tokenStorage.getTokenIdsByIndex(i)).mintAccountId ==
                accountId
            ) {
                tokenIdList[tokenIndex] = tokenStorage.getTokenIdsByIndex(i);
                tokenIndex++;
            }
        }

        // tokenIdListリストから、offsetとlimitに基づいて、tokenListを作成する
        tokenDataList = new RenewableEnergyTokenListData[](size);
        uint256 index = 0;
        for (uint256 i = offset; i < offset + size; i++) {
            bytes32 tokenId = tokenIdList[i];
            tokenDataList[index].tokenId = tokenId;
            tokenDataList[index].tokenStatus = tokenStorage.getTokenById(tokenId).tokenStatus;
            tokenDataList[index].metadataId = tokenStorage.getTokenById(tokenId).metadataId;
            tokenDataList[index].metadataHash = tokenStorage.getTokenById(tokenId).metadataHash;
            tokenDataList[index].mintAccountId = tokenStorage.getTokenById(tokenId).mintAccountId;
            tokenDataList[index].ownerAccountId = tokenStorage.getTokenById(tokenId).ownerAccountId;
            tokenDataList[index].previousAccountId = tokenStorage
                .getTokenById(tokenId)
                .previousAccountId;
            tokenDataList[index].isLocked = tokenStorage.getTokenById(tokenId).isLocked;
            index++;
        }
    }

    /**
     * @dev トークンの詳細情報を取得する
     *
     * @param tokenId トークンID
     * @return renewableEnergyTokenData トークンデータ
     * @return err エラーメッセージ
     */
    function getToken(IRenewableEnergyTokenStorage tokenStorage, bytes32 tokenId)
        external
        view
        returns (RenewableEnergyTokenData memory renewableEnergyTokenData, string memory err)
    {
        if (tokenId == 0x00) {
            return (renewableEnergyTokenData, Error.RV0015_RETOKEN_INVALID_VAL);
        }

        if (tokenStorage.getTokenById(tokenId).tokenStatus == TokenStatus.Empty) {
            return (renewableEnergyTokenData, Error.GE0112_RETOKEN_NOT_EXIST);
        }

        return tokenStorage.getToken(tokenId);
    }

    /**
     * @dev トークンが移転可能かどうかチェックする
     *
     * @param contractManagerAddr コントラクトマネージャーアドレス
     * @param sendAccountId 送信者のアカウントID
     * @param fromAccountId 移転元のアカウントID
     * @param toAccountId 移転先のアカウントID
     * @param miscValue1 miscValue1
     * @param miscValue2Arrays miscValue2
     * @return success true:OK,false:NG
     * @return err エラーメッセージ
     */
    function checkTransactionIsValid(
        IRenewableEnergyTokenStorage tokenStorage,
        address contractManagerAddr,
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 miscValue1,
        string[] memory miscValue2Arrays
    ) external view returns (bool success, string memory err) {
        IContractManager contractManager = IContractManager(contractManagerAddr);
        // miscValue1の値がrenewableでない場合はエラー
        {
            if (miscValue1 != bytes32("renewable")) {
                return (false, Error.RETOKEN_INVALID_MISC1);
            }
        }
        // miscValue2に含まれる要素数が100を超える場合はエラー
        {
            if (miscValue2Arrays.length >= 100) {
                return (false, Error.RETOKEN_INVALID_MISC2);
            }
        }
        // sendAccountIdのステータス確認
        {
            // sendAccount有効性確認
            (success, err) = contractManager.account().isActivated(sendAccountId);
            if (!success) {
                return (success, err);
            }
        }
        // fromAccountIdのステータス確認
        {
            // fromAccount有効性確認
            (success, err) = contractManager.account().isActivated(fromAccountId);
            if (!success) {
                return (success, err);
            }
        }
        // toAccountIdのステータス確認
        {
            // toAccount有効性確認
            (success, err) = contractManager.account().isActivated(toAccountId);
            if (!success) {
                return (success, err);
            }
        }
        // fromAccountIdとtoAccountIdが同一の場合はエラー
        {
            if (fromAccountId == toAccountId) {
                return (false, Error.UE2006_RETOKEN_FROM_TO_ARE_SAME);
            }
        }
        // miscValue2で指定された各要素が32bytesであり、全トークンが存在し、fromAccountIdが所有しているか確認
        {
            for (uint256 i; i < miscValue2Arrays.length; i++) {
                if (bytes(miscValue2Arrays[i]).length > 32) {
                    return (false, Error.RETOKEN_INVALID_MISC2);
                }
                bytes32 tokenId = StringUtils.stringToBytes32(miscValue2Arrays[i]);
                if (tokenStorage.getTokenById(tokenId).tokenStatus != TokenStatus.Active) {
                    return (false, Error.GE0112_RETOKEN_NOT_EXIST);
                }
                if (tokenStorage.getTokenById(tokenId).isLocked) {
                    return (false, Error.GE2015_RETOKEN_IS_LOCKED);
                }
                if (tokenStorage.getTokenById(tokenId).ownerAccountId != fromAccountId) {
                    return (false, Error.GA0028_RETOKEN_NOT_OWNER);
                }
            }
        }
        return (true, "");
    }

    /**
     * @dev RenewableEnergyToken全情報取得
     *      既に登録されているRenewableEnergyToken全情報取得を取得する
     *
     * @param index オフセット
     * @return renewableEnergyToken 全Tokenの情報
     */
    function getRenewableEnergyTokenAll(IRenewableEnergyTokenStorage tokenStorage, uint256 index)
        external
        view
        returns (RenewableEnergyTokenAll memory renewableEnergyToken)
    {
        return tokenStorage.getRenewableEnergyTokenAll(index);
    }

    /**
     * @dev 指定されたaccountIdに紐づくTokenIdを取得
     * @param accountId accountId
     * @return tokenIdsByAccountId accountIdに紐づくTokenIdのリスト
     */
    function getTokenIdsByAccountIdAll(IRenewableEnergyTokenStorage tokenStorage, bytes32 accountId)
        external
        view
        returns (TokenIdsByAccountIdAll memory tokenIdsByAccountId)
    {
        return tokenStorage.getTokenIdsByAccountIdAll(accountId);
    }

    /**
     * @dev Tokenの数を返却する。
     */
    function getTokenCount(IRenewableEnergyTokenStorage tokenStorage)
        external
        view
        returns (uint256 count)
    {
        return tokenStorage.getTokenCount();
    }

    /**
     * @dev limitとoffsetで指定したRenewableEnergyTokensを一括取得する
     *
     * @param offset オフセット
     * @param limit 取得件数
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     * @return renewableEnergyTokenAll 全RenewableEnergyTokenの情報
     * @return totalCount 取得件数
     * @return err エラーメッセージ
     */
    function backupRenewableEnergyTokens(
        IRenewableEnergyTokenStorage tokenStorage,
        IContractManager contractManager,
        uint256 offset,
        uint256 limit,
        uint256 deadline,
        bytes memory signature
    )
        external
        view
        returns (
            RenewableEnergyTokenAll[] memory renewableEnergyTokenAll,
            uint256 totalCount,
            string memory err
        )
    {
        // Admin権限を持つかチェック
        bytes32 hash = keccak256(abi.encode(GET_RETOKEN_ALL_SIGNATURE, deadline));
        (bool has, string memory _err) = contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );

        if (!has) {
            err = (bytes(_err).length != 0) ? _err : Error.RV0001_ACTRL_BAD_ROLE;
            return (renewableEnergyTokenAll, 0, err);
        }

        uint256 count = tokenStorage.getTokenCount();

        if (limit == 0 || count == 0) {
            return (renewableEnergyTokenAll, EMPTY_LENGTH, "");
        }
        if (limit > GET_RETOKENS_LIMIT) {
            return (renewableEnergyTokenAll, EMPTY_LENGTH, Error.UE0113_RETOKEN_TOO_LARGE_LIMIT);
        }

        if (offset >= count) {
            return (
                renewableEnergyTokenAll,
                EMPTY_LENGTH,
                Error.UE0114_RETOKEN_OFFSET_OUT_OF_INDEX
            );
        }

        uint256 size = (count >= offset + limit) ? limit : count - offset;
        renewableEnergyTokenAll = new RenewableEnergyTokenAll[](size);

        for (uint256 i = 0; i < size; i++) {
            renewableEnergyTokenAll[i] = tokenStorage.getRenewableEnergyTokenAll(i + offset);
        }

        return (renewableEnergyTokenAll, count, "");
    }

    /**
     * @dev limitとoffsetで指定したTokenIdsByAccountIdsを一括取得する
     *
     * @param offset オフセット
     * @param limit 取得件数
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     * @return tokenIdsByAccountIdAll 全TokenIdsByAccountIdの情報
     * @return totalCount 取得件数
     * @return err エラーメッセージ
     */
    function backupTokenIdsByAccountIds(
        IRenewableEnergyTokenStorage tokenStorage,
        IContractManager contractManager,
        uint256 offset,
        uint256 limit,
        uint256 deadline,
        bytes memory signature
    )
        external
        view
        returns (
            TokenIdsByAccountIdAll[] memory tokenIdsByAccountIdAll,
            uint256 totalCount,
            string memory err
        )
    {
        // Admin権限を持つかチェック
        bytes32 hash = keccak256(abi.encode(GET_RETOKEN_ALL_SIGNATURE, deadline));
        (bool has, string memory _err) = contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );

        if (!has) {
            err = (bytes(_err).length != 0) ? _err : Error.RV0001_ACTRL_BAD_ROLE;
            return (tokenIdsByAccountIdAll, 0, err);
        }

        if (limit == 0 || contractManager.account().getAccountCount() == 0) {
            return (tokenIdsByAccountIdAll, EMPTY_LENGTH, "");
        }
        if (limit > GET_RETOKENS_LIMIT) {
            return (tokenIdsByAccountIdAll, EMPTY_LENGTH, Error.UE0113_RETOKEN_TOO_LARGE_LIMIT);
        }
        if (offset >= contractManager.account().getAccountCount()) {
            return (tokenIdsByAccountIdAll, EMPTY_LENGTH, Error.UE0114_RETOKEN_OFFSET_OUT_OF_INDEX);
        }

        uint256 size = (contractManager.account().getAccountCount() >= offset + limit)
            ? limit
            : contractManager.account().getAccountCount() - offset;
        tokenIdsByAccountIdAll = new TokenIdsByAccountIdAll[](size);

        for (uint256 i = 0; i < size; i++) {
            (bytes32 accountId, ) = contractManager.account().getAccountId(i + offset);
            tokenIdsByAccountIdAll[i] = tokenStorage.getTokenIdsByAccountIdAll(accountId);
        }

        return (tokenIdsByAccountIdAll, contractManager.account().getAccountCount(), "");
    }
}
