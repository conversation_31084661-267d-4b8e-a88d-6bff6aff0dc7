// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/utils/math/SafeMathUpgradeable.sol";
import "./interfaces/ITokenStorage.sol";
import "./interfaces/IContractManager.sol";
import "./interfaces/Struct.sol";
import "./interfaces/Error.sol";
import "./remigration/RemigrationLib.sol";

/**
 * @dev TokenStorageコントラクト
 *      Tokenデータのストレージ管理を行う
 *      CRUDのみを実装し、ビジネスロジックは含まない
 */
contract TokenStorage is Initializable, ITokenStorage {
    using RemigrationLib for *;
    using SafeMathUpgradeable for uint256;

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev TokenLogicコントラクトアドレス */
    address private _tokenLogicAddr;

    /** @dev 現在のトークンID */
    bytes32 private _tokenId;

    /** @dev トークンデータ */
    mapping(bytes32 => TokenData) private _tokenData;

    /* @dev setTokenAllのsignature検証用 */
    string private constant SET_TOKEN_ALL_SIGNATURE = "setTokenAll";

    ///////////////////////////////////
    // modifiers
    ///////////////////////////////////

    /**
     * @dev TokenLogicコントラクトからのみ呼び出し可能を保証するmodifier
     */
    modifier onlyTokenLogic() {
        require(msg.sender == _tokenLogicAddr, Error.RV0006_ISSUER_INVALID_VAL);
        _;
    }

    modifier adminOnly(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) {
        (bool has, string memory _err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(_err).length == 0, _err);
        require(has, Error.RV0001_ACTRL_BAD_ROLE);
        _;
    }

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 初期化関数
     *
     * @param contractManager ContractManagerアドレス
     * @param tokenLogicAddr TokenLogicコントラクトアドレス
     */
    function initialize(IContractManager contractManager, address tokenLogicAddr)
        public
        initializer
    {
        require(
            address(contractManager) != address(0) && tokenLogicAddr != address(0),
            Error.RV0006_ISSUER_INVALID_VAL
        );
        _contractManager = contractManager;
        _tokenLogicAddr = tokenLogicAddr;
    }

    /**
     * @dev コントラクトバージョン取得
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // TokenData CRUD操作
    ///////////////////////////////////

    /**
     * @dev トークンデータを取得する
     * @param tokenId トークンID
     * @return tokenData トークンデータ
     */
    function getTokenData(bytes32 tokenId)
        external
        view
        override
        returns (TokenData memory tokenData)
    {
        return _tokenData[tokenId];
    }

    /**
     * @dev トークンデータを設定する
     * @param tokenId トークンID
     * @param tokenData トークンデータ
     */
    function setTokenData(bytes32 tokenId, TokenData memory tokenData)
        external
        override
        onlyTokenLogic
    {
        _tokenData[tokenId] = tokenData;
    }

    /**
     * @dev トークンの名前とシンボルを更新する
     * @param tokenId トークンID
     * @param name 新しい名前
     * @param symbol 新しいシンボル
     */
    function updateToken(
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol
    ) external override onlyTokenLogic {
        if (name != 0x00) {
            _tokenData[tokenId].name = name;
        }
        if (symbol != 0x00) {
            _tokenData[tokenId].symbol = symbol;
        }
    }

    /**
     * @dev トークンの有効性を設定する
     * @param tokenId トークンID
     * @param enabled 有効性フラグ
     */
    function setTokenEnabled(bytes32 tokenId, bool enabled) external override onlyTokenLogic {
        _tokenData[tokenId].enabled = enabled;
    }

    /**
     * @dev トークンの総供給量を増加させる
     * @param tokenId トークンID
     * @param amount 増加量
     */
    function addTotalSupply(bytes32 tokenId, uint256 amount) external override onlyTokenLogic {
        // TODO: 言語仕様としてチェックされるので不要
        require(
            _tokenData[tokenId].totalSupply + amount >= _tokenData[tokenId].totalSupply,
            Error.SR0003_TOKEN_OVERFLOW
        );
        _tokenData[tokenId].totalSupply = _tokenData[tokenId].totalSupply.add(amount);
    }

    /**
     * @dev トークンの総供給量を減少させる
     * @param tokenId トークンID
     * @param amount 減少量
     */
    function subTotalSupply(bytes32 tokenId, uint256 amount) external override onlyTokenLogic {
        // TODO: 言語仕様としてチェックされるので不要
        require(_tokenData[tokenId].totalSupply >= amount, Error.SR0004_TOKEN_UNDERFLOW);
        _tokenData[tokenId].totalSupply = _tokenData[tokenId].totalSupply.sub(amount);
    }

    ///////////////////////////////////
    // TokenId管理
    ///////////////////////////////////

    /**
     * @dev 現在のトークンIDを取得する
     * @return tokenId 現在のトークンID
     */
    function getTokenId() external view override returns (bytes32 tokenId) {
        return _tokenId;
    }

    /**
     * @dev 現在のトークンIDを設定する
     * @param tokenId 設定するトークンID
     */
    function setTokenId(bytes32 tokenId) external override onlyTokenLogic {
        _tokenId = tokenId;
    }

    ///////////////////////////////////
    // バックアップ・リストア用
    ///////////////////////////////////

    /**
     * @dev 全トークンデータを設定する（バックアップ・リストア用）
     * @param token 全トークンデータ
     */
    function setTokenAll(
        TokenAll memory token,
        uint256 deadline,
        bytes memory signature
    )
        external
        override
        adminOnly(keccak256(abi.encode(SET_TOKEN_ALL_SIGNATURE, deadline)), deadline, signature)
    {
        _tokenId = token.tokenId;
        RemigrationLib.setTokenAll(_tokenData, token);
    }

    /**
     * @dev 全トークンデータを取得する（バックアップ・リストア用）
     * @return token 全トークンデータ
     */
    function getTokenAll() external view override returns (TokenAll memory token) {
        return RemigrationLib.getTokenAll(_tokenData, _tokenId);
    }
}
