// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "../interfaces/IIBCToken.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

contract IBCTokenMock is IIBCToken {
    struct MockAccountData {
        uint256 balance;
    }

    /* @dev AccountData */
    mapping(bytes32 => MockAccountData) private _accountData;
    /* @dev BalanceByZone */
    mapping(uint16 => mapping(bytes32 => MockAccountData)) private _balanceByZone;
    /* @dev finZoneId */
    uint16 private constant _finZoneId = 3000;
    /* @dev bizZoneId */
    uint16 private constant _bizZoneId = 3001;

    ///////////////////////////////////
    // Mock functions
    ///////////////////////////////////

    // テスト用 updateBusinessZoneBalance
    function syncBusinessZoneBalance(SyncBuisinessZoneBlanaceParams memory params)
        external
        override
    {
        _balanceByZone[params.fromZoneId][params.toAccountId].balance = params.amount;
    }

    // テスト用 残高確認関数
    function balanceOf(uint16, bytes32 accountId)
        external
        view
        returns (uint256 balance, string memory)
    {
        return (_accountData[accountId].balance, "");
    }

    // テスト用 issuerVoucher
    function issueVoucher(
        bytes32 accountId,
        uint256 amount,
        bytes32
    ) external {
        _accountData[accountId].balance += amount;
    }

    // テスト用 getBalanceByZone
    function getBalanceByZone(bytes32 accountId, uint16 zoneId)
        external
        view
        returns (uint256 balance)
    {
        return _balanceByZone[zoneId][accountId].balance;
    }

    // テスト用 updateBalance
    function updateBalance(
        bytes32 accountId,
        uint256 amount,
        bool isAddition
    ) external {
        if (isAddition) {
            _accountData[accountId].balance += amount;
        } else {
            _accountData[accountId].balance -= amount;
        }
    }

    // redeemVoucher
    function redeemVoucher(
        bytes32 accountId,
        uint256 amount,
        bytes32
    ) external override {
        _accountData[accountId].balance -= amount;
    }

    // initAccountBalance
    function initAccountBalance(bytes32 accountId) external override {
        _accountData[accountId].balance = 0;
    }

    // discharge
    function discharge(
        bytes32 accountId,
        uint16 fromZoneId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        bytes32 validatorId = "x5000";
        emit DischargeRequested(validatorId, accountId, fromZoneId, amount, traceId);
    }

    ///////////////////////////////////
    // Override functions
    ///////////////////////////////////

    // Escrow-->Account
    function transferFromEscrow(
        uint16,
        bytes32,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32
    ) external override {
        //残高の確認を行う
        require(_accountData[fromAccountId].balance >= amount, Error.UE4402_BALANCE_NOT_ENOUGH);
        // EscrowAccountからAccountに送金
        _accountData[fromAccountId].balance -= amount;
        _accountData[toAccountId].balance += amount;
    }

    // Account-->Escrow
    function transferToEscrow(
        uint16,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32
    ) external override {
        //残高の確認を行う
        require(_accountData[fromAccountId].balance >= amount, Error.UE4402_BALANCE_NOT_ENOUGH);
        // AccountからEscrowAccountに送金
        _accountData[fromAccountId].balance -= amount;
        _accountData[toAccountId].balance += amount;
    }

    function checkAdminRole(
        bytes32,
        uint256,
        bytes memory
    ) external pure override returns (bool has, string memory err) {
        return (true, "");
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////
}
