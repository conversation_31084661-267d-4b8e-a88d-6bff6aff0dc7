// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import "./interfaces/IContractManager.sol";
import "./interfaces/Error.sol";
import "./ValidatorLogic.sol";
import "./interfaces/IIBCToken.sol";
import "./interfaces/Struct.sol";
import "./libraries/TokenLogicCallLib.sol";
import "./libraries/TokenLogicExecuteLib.sol";

contract IBCToken is Initializable, IIBCToken {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /* @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /* @dev FinZoneのID */
    uint16 private constant _FINANCIAL_ZONE = 3000;
    /* @dev BizZoneのID */
    uint16 private constant _BUSINESS_ZONE = 3001;
    /* @dev issueVoucherの計算パターン */
    uint256 private constant _CALC_ISSUE = 1;
    /* @dev redeemVoucherの計算パターン */
    uint256 private constant _CALC_REDEEM = 2;
    /* @dev resetVoucherの計算パターン */
    uint256 private constant _CALC_RESET = 3;

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     *
     * @param contractManager ContractManagerアドレス
     */
    function initialize(IContractManager contractManager) public initializer {
        _contractManager = contractManager;
    }

    /**
     * @dev コントラクトバージョン取得。
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev Voucherを償却する。
     *
     * ```
     * emit event: RedeemVoucher()
     * ```
     *
     * @param accountId accountId
     * @param amount 償却額
     * @param traceId トレースID
     */
    function redeemVoucher(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        //IBCでなければ実行させない
        require(
            msg.sender == address(_contractManager.ibcApp(Constant._JPY_TOKEN_TRANSFER)),
            Error.GA0022_NOT_IBC_CONTRACT
        );

        //Balanceを取得
        (uint256 balance, ) = _contractManager.account().balanceOf(accountId);

        //残高確認
        require(balance >= amount, Error.UE4402_BALANCE_NOT_ENOUGH);

        //残高更新
        balance = _contractManager.account().editBalance(accountId, amount, _CALC_REDEEM);

        //TotalSupplyを減額
        _contractManager.token().subTotalSupply(amount);

        (uint16 zoneId, , string memory err) = _contractManager.provider().getZone();
        require(bytes(err).length == 0, err);

        //Accountに紐づくvalidatorIdを取得(bcmonitoringのフィルタリング用)
        (bytes32 validatorId, ) = _contractManager.account().getValidatorIdByAccountId(accountId);

        // Account名義返却のため、アカウント情報を取得
        (AccountDataWithoutZoneId memory accountData, ) = _contractManager.account().getAccount(
            accountId
        );
        string memory accountName = accountData.accountName;

        emit RedeemVoucher(zoneId, validatorId, accountId, accountName, amount, balance, traceId);

        // 取引後残高連携
        _contractManager.account().emitAfterBalance(accountId, Constant._EMPTY_VALUE, traceId);
    }

    /**
     * @dev Voucherを発行する。
     *
     * ```
     * emit event: IssueVoucher()
     * ```
     *
     * @param accountId accountId
     * @param amount 発行額
     * @param traceId トレースID
     */
    function issueVoucher(
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        //IBCでなければ実行させない
        require(
            msg.sender == address(_contractManager.ibcApp(Constant._JPY_TOKEN_TRANSFER)),
            Error.GA0022_NOT_IBC_CONTRACT
        );

        // アカウントの解約確認
        TokenLogicCallLib.checkAccountTermination(_contractManager, accountId);

        //TotalSupplyを増額
        _contractManager.token().addTotalSupply(amount);

        //balanceにamountを増額
        uint256 balance = _contractManager.account().editBalance(accountId, amount, _CALC_ISSUE);

        (uint16 zoneId, , string memory err) = _contractManager.provider().getZone();
        require(bytes(err).length == 0, err);

        // Account名義返却のため、アカウント情報を取得
        (AccountDataWithoutZoneId memory accountData, ) = _contractManager.account().getAccount(
            accountId
        );
        string memory accountName = accountData.accountName;

        //Accountに紐づくvalidatorIdを取得(bcmonitoringのフィルタリング用)
        (bytes32 validatorId, ) = _contractManager.account().getValidatorIdByAccountId(accountId);

        emit IssueVoucher(zoneId, validatorId, accountId, accountName, amount, balance, traceId);

        // 取引後残高連携
        _contractManager.account().emitAfterBalance(accountId, Constant._EMPTY_VALUE, traceId);
    }

    /**
     * @dev 後続のDischarge処理を開始するためのイベントを発行する
     *
     * @param accountId アカウントID
     * @param fromZoneId ディスチャージ元のゾーンID
     * @param amount ディスチャージ額
     * @param traceId トレースID
     */
    function discharge(
        bytes32 accountId,
        uint16 fromZoneId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        //IBCでなければ実行させない
        require(
            msg.sender == address(_contractManager.ibcApp(Constant._JPY_TOKEN_TRANSFER)),
            Error.GA0022_NOT_IBC_CONTRACT
        );

        // フィルタリング用のvalidatorIdを取得
        (bytes32 validatorId, ) = _contractManager.account().getValidatorIdByAccountId(accountId);

        emit DischargeRequested(validatorId, accountId, fromZoneId, amount, traceId);
    }

    /**
     * @dev IBC用 Escrow --> Account。
     *
     * @param sendAccountId sendAccountId
     * @param fromAccountId fromAccountId
     * @param toAccountId toAccountId
     * @param amount 送金額
     * @param traceId トレースID
     */
    function transferFromEscrow(
        uint16 fromZoneId,
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        //IBCでなければ実行させない
        require(
            msg.sender == address(_contractManager.ibcApp(Constant._JPY_TOKEN_TRANSFER)),
            Error.GA0022_NOT_IBC_CONTRACT
        );

        // ディスチャージとなるのでfinZone管理のビジネスゾーン残高を減算する
        _contractManager.businessZoneAccount().subtractBusinessZoneBalance(
            fromZoneId,
            toAccountId,
            amount
        );

        // アカウントの限度額更新
        _contractManager.financialZoneAccount().syncDischarge(toAccountId, amount, traceId);

        TransferData memory data;
        {
            data.transferType = Constant._DISCHARGE;
            data.bizZoneId = fromZoneId;
            data.sendAccountId = sendAccountId;
            data.fromAccountId = fromAccountId;
            data.toAccountId = toAccountId;
            data.amount = amount;
        }
        _localTransfer(data, traceId);
    }

    /**
     * @dev IBC用 Account --> Escrow。
     *
     * @param fromAccountId fromAccountId
     * @param toAccountId toAccountId
     * @param amount 送金額
     * @param traceId トレースID
     */
    function transferToEscrow(
        uint16 fromZoneId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        //IBCでなければ実行させない
        require(
            msg.sender == address(_contractManager.ibcApp(Constant._JPY_TOKEN_TRANSFER)),
            Error.GA0022_NOT_IBC_CONTRACT
        );

        // チャージとなるのでfinZone管理のビジネスゾーン残高を加算する
        _contractManager.businessZoneAccount().addBusinessZoneBalance(
            fromZoneId,
            fromAccountId,
            amount
        );

        // アカウントの限度額更新
        _contractManager.financialZoneAccount().syncCharge(fromAccountId, amount, traceId);

        TransferData memory data;
        {
            data.transferType = Constant._CHARGE;
            data.bizZoneId = fromZoneId;
            data.sendAccountId = fromAccountId;
            data.fromAccountId = fromAccountId;
            data.toAccountId = toAccountId;
            data.amount = amount;
        }
        // EscrowAccountにLocalTransferを行う
        _localTransfer(data, traceId);
    }

    /**
     * @dev 送金(内部関数)。
     *
     * ```
     * emit event: Transfer()
     * ```
     *
     * @param data TransferData
     */
    function _localTransfer(TransferData memory data, bytes32 traceId) internal {
        TransferData memory emitData = TokenLogicExecuteLib.transfer(_contractManager, data);

        emit Transfer(emitData, traceId);
    }

    /**
     * @dev IBC用　残高同期処理
     *
     * @param params BizZone内送金の残高更新のデータ
     */
    function syncBusinessZoneBalance(SyncBuisinessZoneBlanaceParams memory params)
        external
        override
    {
        // IBCでなければ実行させない
        require(
            msg.sender == address(_contractManager.ibcApp(Constant._BALANCE_SYNC)),
            Error.GA0022_NOT_IBC_CONTRACT
        );

        // 送信元アカウントの限度額更新
        _contractManager.financialZoneAccount().syncTransfer(
            params.fromAccountId,
            params.amount,
            params.traceId
        );

        // 残高を更新
        _contractManager.businessZoneAccount().syncBusinessZoneBalance(params);
    }

    /**
     * @dev Biz Zone のアカウントの残高を初期化(0)する。（強制償却済みのみ）
     *
     * @param accountId accountId
     */
    function initAccountBalance(bytes32 accountId) external override {
        //IBCでなければ実行させない
        require(
            msg.sender == address(_contractManager.ibcApp(Constant._ACCOUNT_SYNC)),
            Error.GA0022_NOT_IBC_CONTRACT
        );

        //リセット前の残高取得
        (uint256 balance, ) = _contractManager.account().balanceOf(accountId);

        //残高初期化
        _contractManager.account().editBalance(accountId, 0, _CALC_RESET);

        //リセット前の残高分TotalSupplyを減額
        _contractManager.token().subTotalSupply(balance);
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev IBC用 Admin権限チェック。
     *
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     * @return has true:権限あり,false:権限なし
     * @return err エラーメッセージ
     */
    function checkAdminRole(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view override returns (bool has, string memory err) {
        return _contractManager.accessCtrl().checkAdminRole(hash, deadline, signature);
    }
}
