// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import "./interfaces/IContractManager.sol";
import "./interfaces/Error.sol";
import "./interfaces/Struct.sol";

import "./libraries/AccountLib.sol";
import "./remigration/RemigrationLib.sol";

/**
 * @dev Accountコントラクト
 */
contract Account is Initializable, IAccount {
    using AccountLib for *;

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev アカウントID */
    bytes32[] private _accountIds;
    /** @dev アカウントIDの存在確認フラグ(accountId => boolean) */
    mapping(bytes32 => bool) private _accountIdExistence;
    /** @dev アカウントデータ(accountId => AccountData) */
    mapping(bytes32 => AccountData) private _accountData;
    /** @dev アカウントデータ(ownerId => AllowanceList) */
    mapping(bytes32 => AllowanceList) private _accountApprovalData;

    /** @dev 検索結果の最大取得件数 */
    uint256 private constant _MAX_LIMIT = 100;
    /** @dev 未登録の場合にて返す空の件数 **/
    uint256 private constant _EMPTY_LENGTH = 0;

    /** @dev バリデーション用のステータス値(アクティブ) */
    bytes32 private constant _STATUS_ACTIVE = "active";
    /** @dev バリデーション用のステータス値(凍結) */
    bytes32 private constant _STATUS_FROZEN = "frozen";
    /** @dev バリデーション用のステータス値(解約済み) */
    bytes32 private constant _STATUS_TERMINATED = "terminated";

    /** @dev GET ALL関数の最大取得件数 */
    uint256 private constant _GET_ACCOUNTS_LIMIT = 1000;
    /** @dev getAccountsAllのsignature検証用 **/
    string private constant _GET_ACCOUNTS_ALL_SIGNATURE = "getAccountsAll";
    /* @dev setAccountsAllのsignature検証用 */
    string private constant _SET_ACCOUNTS_ALL_SIGNATURE = "setAccountsAll";

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     *
     * @param contractManager contractManager
     */
    function initialize(IContractManager contractManager) public initializer {
        _contractManager = contractManager;
    }

    /**
     * @dev コントラクトバージョン取得。
     * @return version コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    /**
     * @dev Admin権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(内部関数)。
     *
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     */
    function _adminOnly(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) internal view {
        (bool has, string memory err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.ACCOUNT_NOT_ADMIN);
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev 共通領域 アカウント登録。
     *
     * @param accountId accountId
     * @param accountName アカウント名
     */
    function addAccount(
        bytes32 accountId,
        string memory accountName,
        bytes32 validatorId
    ) external override {
        // Validatorコントラクトからの呼び出しである事が条件
        require(
            msg.sender == address(_contractManager.validator()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );
        // Accountの未入力チェック確認
        {
            require(accountId != 0x00, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }
        // ID重複確認
        {
            require(!_accountIdExistence[accountId], Error.GE1010_ACCOUNT_ID_EXIST);
        }

        // Account登録
        _accountIds.addAccountId(accountId);
        _accountIdExistence.addAccountIdExistence(accountId, true);
        _accountData.addAccountData(accountId, accountName);
        _accountData.addValidatorId(accountId, validatorId);
    }

    /**
     * @dev アカウント名変更
     *
     * @param accountId accountId
     * @param accountName アカウント名
     * @param traceId traceId
     */
    function modAccount(
        bytes32 accountId,
        string memory accountName,
        bytes32 traceId
    ) external {
        // Validatorコントラクトからの呼び出しである事が条件
        require(
            msg.sender == address(_contractManager.validator()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );

        // Account名変更
        _accountData.modAccount(accountId, accountName);

        emit ModAccount(accountId, accountName, traceId);
    }

    /**
     * @dev AccountのRoleを追加する。
     * ```
     * emit event: AddAccountRole()
     * ```
     *
     * @param accountId accountId
     * @param accountEoa accountEoa
     * @param traceId トレースID
     */
    function addAccountRole(
        bytes32 accountId,
        address accountEoa,
        bytes32 traceId
    ) external override {
        // issuerコントラクトからの呼び出しである事が条件
        require(msg.sender == address(_contractManager.issuer()), Error.GA0008_NOT_ISSUER_CONTRACT);
        // accountId存在チェック
        {
            (bool success, string memory errTmp) = _hasAccount(accountId);
            require(success, errTmp);
        }
        //Eoaが空である場合はError
        require(accountEoa != address(0), Error.RV0007_ACCOUNT_INVALID_VAL);

        // EOAを追加する
        _contractManager.accessCtrl().addAccountEoa(accountId, accountEoa);

        // Emit Event
        emit AddAccountRole(accountId, accountEoa, traceId);
    }

    /**
     * @dev Accountの有効性を更新する。(アクティブ or 凍結)
     * ```
     * emit event: AccountEnabled()
     * ```
     *
     * @param accountId accountId
     * @param accountStatus アカウントステータス
     * @param reasonCode reasonCode
     */
    function setAccountStatus(
        bytes32 accountId,
        bytes32 accountStatus,
        bytes32 reasonCode,
        bytes32 traceId
    ) external override {
        _accountData.setAccountStatus(
            address(_contractManager),
            msg.sender,
            accountId,
            accountStatus,
            reasonCode
        );

        emit AccountEnabled(accountId, accountStatus, reasonCode, traceId);
    }

    /**
     * @dev Accountの解約
     *
     * @param accountId accountId
     * @param reasonCode reasonCode
     * @param traceId トレースID
     */
    function setTerminated(
        bytes32 accountId,
        bytes32 reasonCode,
        bytes32 traceId
    ) external override {
        _accountData.setTerminated(address(_contractManager), msg.sender, accountId, reasonCode);

        emit AccountTerminated(accountId, reasonCode, traceId);
    }

    /**
     * @dev 連携済みzone情報の追加
     *
     * @param accountId accountId
     * @param zoneId zoneId
     * @param traceId トレースID
     */
    function addZone(
        bytes32 accountId,
        uint16 zoneId,
        bytes32 traceId
    ) external override {
        //Account存在確認
        {
            (bool success, string memory errTmp) = _hasAccount(accountId);
            require(success, errTmp);
        }
        _accountData.addZone(accountId, zoneId);

        emit AddZone(accountId, zoneId, traceId);
    }

    /**
     * @dev 送金許可設定。
     *
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @param amount 許容額
     */
    function approve(
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount
    ) external override {
        // Tokenコントラクトからの呼び出しである事が条件
        require(msg.sender == address(_contractManager.token()), Error.GA0016_NOT_TOKEN_CONTRACT);

        _accountApprovalData.setApproval(
            ownerId,
            spenderId,
            _accountData[spenderId].accountName,
            _contractManager.financialZoneAccount().getJSTDay(),
            amount
        );
    }

    /**
     * @dev 発行。
     *
     * @param accountId accountId
     * @param amount mint額
     * @return balance mint後の残高
     */
    function mint(bytes32 accountId, uint256 amount) external override returns (uint256) {
        // Tokenコントラクトからの呼び出しである事が条件
        require(msg.sender == address(_contractManager.token()), Error.GA0016_NOT_TOKEN_CONTRACT);
        // 呼び出し元のTokenで残高をemitするため、残高の更新とreturnを行う
        return _accountData.setBalance(accountId, amount, true);
    }

    /**
     * @dev 償却。
     *
     * @param accountId accountId
     * @param amount burn額
     * @return balance burn後の残高
     */
    function burn(bytes32 accountId, uint256 amount) external override returns (uint256) {
        // Tokenコントラクトからの呼び出しである事が条件
        require(msg.sender == address(_contractManager.token()), Error.GA0016_NOT_TOKEN_CONTRACT);
        // 呼び出し元のTokenで残高をemitするため、残高の更新とreturnを行う
        return _accountData.setBalance(accountId, amount, false);
    }

    /**
     * @dev Accountの残高を強制償却する
     * 最初にアカウントの凍結状態をisFrozenで確認し、凍結状態でない場合はエラーを返す
     *
     * @param accountId accountId
     * @param traceId traceId
     */
    function forceBurn(bytes32 accountId, bytes32 traceId) external override {
        // 全残高情報と合計値を取得
        // （Issuerコントラクトからの呼び出し権限をチェックするgetAllBalanceWithAuthを使用）
        (, uint256 totalBalance) = _accountData.getAllBalanceWithAuth(
            address(_contractManager),
            accountId
        );

        // Accountの残高をBizZone含め全て強制償却する
        (
            uint256 burnedAmount,
            uint256 burnedBalance,
            ForceDischarge[] memory forceDischarge
        ) = _accountData.forceBurn(address(_contractManager), accountId);

        //Accountに紐づくvalidatorIdを取得(bcmonitoringのフィルタリング用)
        (bytes32 validatorId, ) = this.getValidatorIdByAccountId(accountId);

        // 取引後残高連携
        this.emitAfterBalance(accountId, Constant._EMPTY_VALUE, traceId);

        emit ForceBurn(
            validatorId,
            accountId,
            traceId,
            totalBalance,
            burnedAmount,
            burnedBalance,
            forceDischarge
        );
    }

    /**
     * @dev AccountのBalanceを部分的に強制償却
     *
     * @param accountId accountId
     * @param burnedAmount 償却する金額
     * @param burnedBalance 償却後に残す金額
     * @param traceId traceId
     */
    function partialForceBurn(
        bytes32 accountId,
        uint256 burnedAmount,
        uint256 burnedBalance,
        bytes32 traceId
    ) external override {
        // 全残高情報と合計値を取得
        // （Issuerコントラクトからの呼び出し権限をチェックするgetAllBalanceWithAuthを使用）
        (, uint256 totalBalance) = _accountData.getAllBalanceWithAuth(
            address(_contractManager),
            accountId
        );

        // Accountの残高を部分的に強制償却する
        ForceDischarge[] memory forceDischarge = _accountData.partialForceBurn(
            _contractManager,
            accountId,
            burnedAmount,
            burnedBalance
        );

        //Accountに紐づくvalidatorIdを取得(bcmonitoringのフィルタリング用)
        (bytes32 validatorId, ) = this.getValidatorIdByAccountId(accountId);

        // 取引後残高連携
        this.emitAfterBalance(accountId, Constant._EMPTY_VALUE, traceId);

        emit ForceBurn(
            validatorId,
            accountId,
            traceId,
            totalBalance,
            burnedAmount,
            burnedBalance,
            forceDischarge
        );
    }

    /**
     * @dev FromからToへ送金指示。
     *
     * @param fromAccount 送金元AccountのID
     * @param toAccount 送金先AccountのID
     * @param amount 送金額
     * @return fromAccountBalance 送金元アカウントの残高
     * @return toAccountBalance 送金先アカウントの残高
     */
    function calcBalance(
        bytes32 fromAccount,
        bytes32 toAccount,
        uint256 amount
    ) external override returns (uint256 fromAccountBalance, uint256 toAccountBalance) {
        //Tokenのコントラクトではないとエラー
        require(
            (msg.sender == address(_contractManager.token()) ||
                msg.sender == address(_contractManager.ibcToken())),
            Error.GA0016_NOT_TOKEN_CONTRACT
        );
        // 取引明細用にBalanceを返却
        return (
            _accountData.setBalance(fromAccount, amount, false),
            _accountData.setBalance(toAccount, amount, true)
        );
    }

    /**
     * @dev 送金許可額の減額を行う。
     *
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @param amount 送金額
     */
    function calcAllowance(
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount
    ) external override {
        //Tokenのコントラクトではないとエラー
        require(msg.sender == address(_contractManager.token()), Error.GA0016_NOT_TOKEN_CONTRACT);
        // allowanceの減額を行う
        _accountApprovalData.setAllowance(ownerId, spenderId, amount);
    }

    /**
     * @dev IssueVoucher, RedeemVoucherから呼ばれる残高の更新。
     *
     * @param accountId accountId
     * @param amount 残高の更新額
     * @param calcPattern 1:残高に加算 2:残高より減算
     * @return balance 更新後の残高
     */
    function editBalance(
        bytes32 accountId,
        uint256 amount,
        uint256 calcPattern
    ) external override returns (uint256 balance) {
        //Tokenのコントラクトではないとエラー
        require(
            (msg.sender == address(_contractManager.token()) ||
                msg.sender == address(_contractManager.ibcToken())),
            Error.GA0016_NOT_TOKEN_CONTRACT
        );

        //issuerVoucher
        if (calcPattern == 1) {
            return _accountData.setBalance(accountId, amount, true);
        }
        //redeemVoucher
        else if (calcPattern == 2) {
            return _accountData.setBalance(accountId, amount, false);
        }
        //resetVoucher
        else if (calcPattern == 3) {
            return
                _accountData.setBalance(
                    accountId,
                    _accountData.getAccountBalance(accountId),
                    false
                );
        }
        // 取引明細用にBalanceを返却
        return _accountData[accountId].balance;
    }

    /**
     * @dev 指定されたAccountIdに紐づくAccount情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     */
    function setAccountAll(
        AccountsAll memory account,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(_SET_ACCOUNTS_ALL_SIGNATURE, deadline));
            _adminOnly(hash, deadline, signature);
        }

        RemigrationLib.setAccountAll(
            _accountIds,
            _accountData[account.accountId],
            _accountIdExistence,
            _accountApprovalData,
            address(_contractManager),
            account
        );
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev AccountID存在確認。
     *
     * @param accountId accountId
     * @return success true:未登録, false:登録済
     * @return err エラーメッセージ
     */
    function hasAccount(bytes32 accountId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return _hasAccount(accountId);
    }

    /**
     * @dev Account有効状態確認
     *
     * @param accountId accountId
     * @return success true:有効, false:無効、凍結済み
     */
    function isActivated(bytes32 accountId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        (success, err) = _hasAccount(accountId);
        if (!success) {
            return (false, err);
        }
        return _accountData.isActivated(accountId);
    }

    /**
     * @dev  Accountの解約フラグの確認をする。
     *
     * @param accountId accountId
     * @return terminated true:アカウントが解約済, false:アカウントが未解約
     * @return err エラーメッセージ
     */
    // TODO terminated ではなくaccountStatus?
    function isTerminated(bytes32 accountId)
        external
        view
        override
        returns (bool terminated, string memory err)
    {
        // Account存在確認
        {
            bool success;
            (success, err) = _hasAccount(accountId);
            if (!success) {
                return (false, err);
            }
        }
        return (_accountData.isTerminated(accountId), "");
    }

    /**
     * @dev AccountID存在確認 本体(内部関数)。
     *
     * @param accountId accountId
     * @return success true:未登録, false:登録済
     * @return err エラーメッセージ
     */
    function _hasAccount(bytes32 accountId)
        internal
        view
        returns (bool success, string memory err)
    {
        if (accountId == 0x00) {
            return (false, Error.RV0007_ACCOUNT_INVALID_VAL);
        }
        if (!_accountIdExistence[accountId]) {
            return (false, Error.GE0105_ACCOUNT_ID_NOT_EXIST);
        }
        return (true, "");
    }

    /**
     * @dev 残高の取得。
     *
     * @param accountId accountId
     * @return balance 残高
     * @return err エラーメッセージ
     */
    function balanceOf(bytes32 accountId)
        external
        view
        override
        returns (uint256 balance, string memory err)
    {
        if (
            msg.sender != address(_contractManager.issuer()) &&
            msg.sender != address(_contractManager.ibcToken())
        ) {
            return (0, Error.INVALID_CALLER_ADDRESS);
        }
        bool success;
        (success, err) = _hasAccount(accountId);
        if (!success) {
            return (0, err);
        }
        return (_accountData.getAccountBalance(accountId), "");
    }

    /**
     * @dev Accountの情報を返す。 TODO: CoreAPIとのマッピング対応時に作成
     *
     * @param accountId accountId
     * @return accountData アカウントデータ(zoneIdなし)
     * @return err
     */
    function getAccount(bytes32 accountId)
        external
        view
        override
        returns (AccountDataWithoutZoneId memory accountData, string memory err)
    {
        bool success;
        (success, err) = _hasAccount(accountId);
        (accountData, ) = _accountData.getAccountData(accountId, success, err);
        return (accountData, err);
    }

    /**
     * @dev 移転先のアカウント情報を取得する
     *
     * @param accountId アカウントID
     * @return accountName アカウント名
     * @return err エラー
     */
    function getDestinationAccount(bytes32 accountId)
        external
        view
        override
        returns (string memory accountName, string memory err)
    {
        // validatorコントラクトからの呼び出しである事が条件
        require(
            msg.sender == address(_contractManager.validator()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );

        AccountDataWithoutZoneId memory accountData;

        (accountData, err) = this.getAccount(accountId);
        if (bytes(err).length != 0) {
            return ("", err);
        }

        if (
            accountData.accountStatus != _STATUS_ACTIVE &&
            accountData.accountStatus != _STATUS_FROZEN
        ) {
            return ("", Error.GE2005_ACCOUNT_DISABLED);
        }

        return (accountData.accountName, "");
    }

    /**
     * @dev Accountの全情報を返す。
     *
     * @param accountId accountId
     * @return accountDataAll アカウントデータ
     * @return err
     */
    function getAccountAll(bytes32 accountId)
        external
        view
        override
        returns (AccountDataAll memory accountDataAll, string memory err)
    {
        // validatorコントラクトからの呼び出しである事が条件
        require(
            msg.sender == address(_contractManager.validator()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );
        // TODO: エラーを返すのでerrをreturnするように修正
        accountDataAll = AccountLib.getAccountDataAll(
            address(_contractManager),
            _accountData,
            accountId
        );

        return (accountDataAll, "");
    }

    /**
     * @dev アカウントに紐づくバリデータIDを取得する
     *
     * @param accountId アカウントID
     * @return validatorId バリデータID
     * @return err エラー
     */
    function getValidatorIdByAccountId(bytes32 accountId)
        external
        view
        override
        returns (bytes32 validatorId, string memory err)
    {
        bool success;
        (success, err) = _hasAccount(accountId);
        if (!success) {
            return (0x00, err);
        }

        return (_accountData.getValidatorIdByAccountId(accountId), "");
    }

    /**
     * @dev IndexよりAccountIDを取得する。
     *
     * @param index index
     * @return accountId accountId
     * @return err エラーメッセージ
     */
    function getAccountId(uint256 index)
        external
        view
        override
        returns (bytes32 accountId, string memory err)
    {
        if (_accountIds.length <= index) {
            return (0x00, Error.UE0106_ACCOUNT_OUT_OF_INDEX);
        }
        return (_accountIds[index], "");
    }

    /**
     * @dev アカウントの限度額を取得
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @return accountLimitData
     * @return err
     */
    function getAccountLimit(bytes32 validatorId, bytes32 accountId)
        external
        view
        override
        returns (FinancialZoneAccountData memory accountLimitData, string memory err)
    {
        // Validatorコントラクトからの呼び出しである事が条件 -> Validatorコントラクトのstack too depp回避のため削除
        // require(msg.sender == address(_contractManager.validator()), Error.NOT_VALIDATOR_CONTRACT);
        // Validatorの有効性を確認
        (bool success, string memory errHasValidator) = _contractManager.validator().hasValidator(
            validatorId
        );
        if (!success) {
            return (accountLimitData, errHasValidator);
        }

        return _contractManager.financialZoneAccount().getAccountLimitData(accountId);
    }

    /**
     * @dev 送金許可設定の取得。
     *
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @return allowance
     * @return approvedAt
     * @return err
     */
    function getAllowance(bytes32 ownerId, bytes32 spenderId)
        external
        view
        override
        returns (
            uint256 allowance,
            uint256 approvedAt,
            string memory err
        )
    {
        // TokenコントラクトかFinancialCheckコントラクトからの呼び出しである事が条件
        require(
            msg.sender == address(_contractManager.token()) ||
                msg.sender == address(_contractManager.financialCheck()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );
        //Account存在確認
        {
            (bool successOwner, ) = _hasAccount(ownerId);
            (bool successSpender, ) = _hasAccount(spenderId);
            if (!successOwner) return (0, 0, Error.UE0104_OWNER_NOT_EXIST);
            else if (!successSpender) return (0, 0, Error.UE0105_SPENDER_NOT_EXIST);
        }
        // allowanceを返す
        (allowance, approvedAt) = _accountApprovalData.getAllowance(ownerId, spenderId);
        return (allowance, approvedAt, "");
    }

    /**
     * @dev 送金許可一覧照会 TODO:Core APIとのマッピング時に作成
     *
     * @param ownerId 送金許可元ID
     * @param offset オフセット
     * @param limit リミット
     * @return approvalData 送金許可設定一覧
     * @return totalCount 総数
     * @return err エラーメッセージ
     */
    function getAllowanceList(
        bytes32 ownerId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        override
        returns (
            AccountApprovalAll[] memory approvalData,
            uint256 totalCount,
            string memory err
        )
    {
        // tokenコントラクトからの呼び出しである事が条件
        require(msg.sender == address(_contractManager.token()), Error.GA0016_NOT_TOKEN_CONTRACT);

        return
            AccountLib.getAllowanceList(
                _accountApprovalData,
                address(_contractManager),
                ownerId,
                offset,
                limit
            );
    }

    /**
     * @dev Accountの数を返却する。
     *
     * @param count accountの数
     */
    function getAccountCount() external view override returns (uint256 count) {
        return _accountIds.length;
    }

    /**
     * @dev Accountの連携済みのzoneIdを返却する。
     *
     * @param accountId accountId
     * @return zones zoneIdのリスト
     */
    function getZoneByAccountId(bytes32 accountId)
        external
        view
        override
        returns (ZoneData[] memory zones)
    {
        uint16[] memory zoneIds = _accountData.getAccountZoneIdList(accountId);
        bytes32[] memory emptyIssuerIds;

        zones = new ZoneData[](zoneIds.length);
        for (uint256 i = 0; i < zoneIds.length; i++) {
            // ZoneコントラクトからzoneNameを取得
            string memory zoneName = _contractManager.provider().getZoneName(zoneIds[i]);

            // 取得したzoneIdとzoneNameでZoneDataを作成し、配列に追加
            zones[i] = ZoneData(zoneIds[i], zoneName, emptyIssuerIds);
        }

        return zones;
    }

    /**
     * @dev Accountが凍結状態となっているか確認する。
     *
     * @param accountId accountId
     * @return frozen 凍結状態
     * @return err エラーメッセージ
     */
    function isFrozen(bytes32 accountId) external view returns (bool frozen, string memory err) {
        //アカウントの存在確認
        (bool success, string memory errTmp) = _hasAccount(accountId);
        if (!success) {
            return (false, errTmp);
        }
        // アカウントの凍結状態を確認
        return (_accountData.isFrozen(accountId), "");
    }

    /**
     * @dev limitとoffsetで指定したAccountsを一括取得する
     *
     */
    function getAccountsAll(uint256 index)
        external
        view
        override
        returns (AccountsAll memory account)
    {
        bytes32 _accountId = _accountIds[index];

        return
            RemigrationLib.getAccountAll(
                _accountData[_accountId],
                _accountIdExistence,
                _accountApprovalData,
                address(_contractManager),
                _accountId
            );
    }

    /**
     * @dev 残高照会後のイベントを発行する
     * @notice アカウントの残高情報を含むイベントを発行し、監査証跡を残す
     *         未登録アカウントの場合でもエラーにならず、空の残高情報でイベントを発行する
     *
     * @param fromAccountId 送金元アカウントID（照会対象）
     * @param toAccountId 送金先アカウントID（照会対象）
     * @param traceId トレースID（イベントの追跡用）
     *
     * @custom:event AfterBalanceEmitted 以下の情報を含むイベントを発行:
     *   - fromBalance: 送金元アカウントの全ゾーン残高（FinZone→BizZone昇順）
     *   - toBalance: 送金先アカウントの全ゾーン残高（FinZone→BizZone昇順）
     *   - traceId: indexed属性付きのトレースID
     */
    function emitAfterBalance(
        bytes32 fromAccountId,
        bytes32 toAccountId,
        bytes32 traceId
    ) external override {
        // イベントの発行
        (AllBalanceData[] memory fromBalance, ) = _accountData.getAllBalance(
            address(_contractManager),
            fromAccountId
        );
        (AllBalanceData[] memory toBalance, ) = _accountData.getAllBalance(
            address(_contractManager),
            toAccountId
        );
        emit AfterBalance(fromBalance, toBalance, traceId);
    }

    ///////////////////////////////////
    // for upgrade contracts
    // deprecated
    ///////////////////////////////////

    // uint256[50] private __gap;
}
