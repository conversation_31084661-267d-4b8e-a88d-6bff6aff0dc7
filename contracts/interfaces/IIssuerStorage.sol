// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./Struct.sol";

/**
 * @dev IssuerStorageインターフェース
 *      Issuerデータのストレージ操作を定義
 *      IssuerLogicコントラクトからのみ呼び出し可能
 */
interface IIssuerStorage {
    ///////////////////////////////////
    // IssuerData CRUD操作
    ///////////////////////////////////

    /**
     * @dev 発行者データを取得する
     * @param issuerId 発行者ID
     * @return issuerData 発行者データ
     */
    function getIssuerData(bytes32 issuerId) external view returns (IssuerData memory issuerData);

    /**
     * @dev 発行者データを設定する
     * @param issuerId 発行者ID
     * @param issuerData 発行者データ
     */
    function setIssuerData(bytes32 issuerId, IssuerData memory issuerData) external;

    /**
     * @dev 発行者データの名前を更新する
     * @param issuerId 発行者ID
     * @param name 新しい名前
     */
    function updateIssuerName(bytes32 issuerId, string memory name) external;

    /**
     * @dev 発行者にアカウントIDを追加する
     * @param issuerId 発行者ID
     * @param accountId 追加するアカウントID
     */
    function addAccountIdToIssuer(bytes32 issuerId, bytes32 accountId) external;

    ///////////////////////////////////
    // IssuerIds配列操作
    ///////////////////////////////////

    /**
     * @dev 発行者IDを配列に追加する
     * @param issuerId 追加する発行者ID
     */
    function addIssuerId(bytes32 issuerId) external;

    /**
     * @dev 発行者IDの配列を取得する
     * @return issuerIds 発行者IDの配列
     */
    function getIssuerIds() external view returns (bytes32[] memory issuerIds);

    /**
     * @dev 発行者IDを指定インデックスで取得する
     * @param index インデックス
     * @return issuerId 発行者ID
     */
    function getIssuerIdByIndex(uint256 index) external view returns (bytes32 issuerId);

    /**
     * @dev 発行者IDの総数を取得する
     * @return count 発行者IDの総数
     */
    function getIssuerIdsCount() external view returns (uint256 count);

    ///////////////////////////////////
    // IssuerIdExistence マッピング操作
    ///////////////////////////////////

    /**
     * @dev 発行者IDの存在フラグを取得する
     * @param issuerId 発行者ID
     * @return exists 存在フラグ
     */
    function getIssuerIdExistence(bytes32 issuerId) external view returns (bool exists);

    /**
     * @dev 発行者IDの存在フラグを設定する
     * @param issuerId 発行者ID
     * @param exists 存在フラグ
     */
    function setIssuerIdExistence(bytes32 issuerId, bool exists) external;

    ///////////////////////////////////
    // AccountIdExistenceByIssuerId マッピング操作
    ///////////////////////////////////

    /**
     * @dev 発行者IDに紐づくアカウントIDの存在フラグを取得する
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @return exists 存在フラグ
     */
    function getAccountIdExistenceByIssuerId(bytes32 issuerId, bytes32 accountId)
        external
        view
        returns (bool exists);

    /**
     * @dev 発行者IDに紐づくアカウントIDの存在フラグを設定する
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param exists 存在フラグ
     */
    function setAccountIdExistenceByIssuerId(
        bytes32 issuerId,
        bytes32 accountId,
        bool exists
    ) external;

    ///////////////////////////////////
    // バックアップ・リストア関連
    ///////////////////////////////////

    /**
     * @dev バックアップ用に全発行者データを設定する（Admin権限必要）
     * @param issuer 全発行者データ
     * @param deadline 署名の期限
     * @param signature Admin署名
     */
    function setIssuerAll(
        IssuerAll memory issuer,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev バックアップ用に発行者データを取得する
     * @param index インデックス
     * @return issuer 全発行者データ
     */
    function getIssuerAll(uint256 index) external view returns (IssuerAll memory issuer);

    /**
     * @dev ContractManagerアドレスを更新する（Admin権限必要）
     * @param contractManagerAddr 新しいContractManagerアドレス
     */
    function setContractManagerAddress(address contractManagerAddr) external;

    /**
     * @dev ContractManagerアドレスを取得する
     * @return contractManagerAddr ContractManagerアドレス
     */
    function getContractManagerAddress() external view returns (address contractManagerAddr);
}
