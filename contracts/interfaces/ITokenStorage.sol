// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./Struct.sol";

/**
 * @dev ITokenStorageインターフェース
 *      Tokenデータのストレージ操作を定義
 *      TokenLogicコントラクトからのみ呼び出し可能
 */
interface ITokenStorage {
    ///////////////////////////////////
    // TokenData CRUD操作
    ///////////////////////////////////

    /**
     * @dev トークンデータを取得する
     * @param tokenId トークンID
     * @return tokenData トークンデータ
     */
    function getTokenData(bytes32 tokenId) external view returns (TokenData memory tokenData);

    /**
     * @dev トークンデータを設定する
     * @param tokenId トークンID
     * @param tokenData トークンデータ
     */
    function setTokenData(bytes32 tokenId, TokenData memory tokenData) external;

    /**
     * @dev トークンの名前とシンボルを更新する
     * @param tokenId トークンID
     * @param name 新しい名前
     * @param symbol 新しいシンボル
     */
    function updateToken(
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol
    ) external;

    /**
     * @dev トークンの有効性を設定する
     * @param tokenId トークンID
     * @param enabled 有効性フラグ
     */
    function setTokenEnabled(bytes32 tokenId, bool enabled) external;

    /**
     * @dev トークンの総供給量を増加させる
     * @param tokenId トークンID
     * @param amount 増加量
     */
    function addTotalSupply(bytes32 tokenId, uint256 amount) external;

    /**
     * @dev トークンの総供給量を減少させる
     * @param tokenId トークンID
     * @param amount 減少量
     */
    function subTotalSupply(bytes32 tokenId, uint256 amount) external;

    ///////////////////////////////////
    // TokenId管理
    ///////////////////////////////////

    /**
     * @dev 現在のトークンIDを取得する
     * @return tokenId 現在のトークンID
     */
    function getTokenId() external view returns (bytes32 tokenId);

    /**
     * @dev 現在のトークンIDを設定する
     * @param tokenId 設定するトークンID
     */
    function setTokenId(bytes32 tokenId) external;

    ///////////////////////////////////
    // バックアップ・リストア用
    ///////////////////////////////////

    /**
     * @dev 全トークンデータを設定する（バックアップ・リストア用）
     * @param token 全トークンデータ
     */
    function setTokenAll(
        TokenAll memory token,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev 全トークンデータを取得する（バックアップ・リストア用）
     * @return token 全トークンデータ
     */
    function getTokenAll() external view returns (TokenAll memory token);
}
