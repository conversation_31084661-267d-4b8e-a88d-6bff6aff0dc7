// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;
import "./Struct.sol";

/**
 * @dev Providerインターフェース
 *      Providerのロジック操作を定義
 */
interface IProviderStorage {
    /**
     * @dev プロバイダデータを取得する
     * @param providerId プロバイダID
     * @return providerData プロバイダデータ
     */
    function getProviderData(bytes32 providerId)
        external
        view
        returns (ProviderData memory providerData);

    /**
     * @dev ZoneIDを取得する。
     * @return zoneId 領域ID (デジタル通貨区分)
     * @return zoneName 領域名 (デジタル通貨区分)
     * @return err bytes(err).length!=0の場合、エラー有り(その他の戻り値は無効)
     */
    function getZone()
        external
        view
        returns (
            uint16 zoneId,
            string memory zoneName,
            string memory err
        );

    /**
     * @dev プロバイダデータを設定する
     * @param providerId プロバイダID
     * @param providerData プロバイダデータ
     */
    function setProviderData(bytes32 providerId, ProviderData memory providerData) external;

    /**
     * @dev プロバイダの名前を更新する
     * @param providerId プロバイダID
     * @param name 新しい名前
     */
    function updateProviderName(bytes32 providerId, bytes32 name) external;

    /**
     * @dev Zone情報を設定する
     * @param zoneId 領域ID
     * @param zoneName 領域名
     */
    function setZone(uint16 zoneId, string memory zoneName) external;

    /**
     * @dev Zone名称を取得する
     * @param zoneId 領域ID
     * @return zoneName 領域名
     */
    function getZoneName(uint16 zoneId) external view returns (string memory zoneName);

    /**
     * @dev プロバイダ数を取得する
     * @return count プロバイダ数
     */
    function getProviderCount() external view returns (uint256 count);

    /**
     * @dev Issuerリストを設定する
     * @param zoneId 領域ID
     * @param issuerIds IssuerIDリスト
     */
    function setAvailableIssuerIds(uint16 zoneId, bytes32[] memory issuerIds) external;

    /**
     * @dev Issuerリストを取得する
     * @param zoneId 領域ID
     * @return issuerIds IssuerIDリスト
     */
    function getAvailableIssuerIds(uint16 zoneId)
        external
        view
        returns (bytes32[] memory issuerIds);

    ///////////////////////////////////
    // ProviderId管理
    ///////////////////////////////////

    /**
     * @dev 現在のプロバイダIDを取得する
     * @return providerId 現在のプロバイダID
     */
    function getProviderId() external view returns (bytes32 providerId);

    /**
     * @dev 現在のプロバイダIDを設定する
     * @param providerId 設定するプロバイダID
     */
    function setProviderId(bytes32 providerId) external;

    ///////////////////////////////////
    // バックアップ・リストア用
    ///////////////////////////////////

    /**
     * @dev 全プロバイダデータを設定する（バックアップ・リストア用）
     * @param provider 全プロバイダデータ
     */
    function setProviderAll(
        ProviderAll memory provider,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev 全プロバイダデータを取得する（バックアップ・リストア用）
     * @return provider 全プロバイダデータ
     */
    function getProviderAll(bytes32 providerId) external view returns (ProviderAll memory provider);
}
