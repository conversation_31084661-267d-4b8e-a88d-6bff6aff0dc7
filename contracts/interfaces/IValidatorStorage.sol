// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "./Struct.sol";

/**
 * @dev ValidatorStorageインターフェース
 *      Validatorデータのストレージ操作を定義
 *      ValidatorLogicコントラクトからのみ呼び出し可能
 */
interface IValidatorStorage {
    ///////////////////////////////////
    // ValidatorData CRUD操作
    ///////////////////////////////////

    /**
     * @dev 検証者データを取得する
     * @param validatorId 検証者ID
     * @return validatorData 検証者データ
     */
    function getValidatorData(bytes32 validatorId)
        external
        view
        returns (ValidatorData memory validatorData);

    /**
     * @dev 検証者データを設定する
     * @param validatorId 検証者ID
     * @param validatorData 検証者データ
     */
    function setValidatorData(bytes32 validatorId, ValidatorData memory validatorData) external;

    /**
     * @dev 検証者データの名前を更新する
     * @param validatorId 検証者ID
     * @param name 新しい名前
     */
    function updateValidatorName(bytes32 validatorId, bytes32 name) external;

    /**
     * @dev 検証者データのロールを更新する
     * @param validatorId 検証者ID
     * @param role 新しいロール
     */
    function updateValidatorRole(bytes32 validatorId, bytes32 role) external;

    /**
     * @dev 検証者データのバリデータアカウントIDを更新する
     * @param validatorId 検証者ID
     * @param validatorAccountId 新しいバリデータアカウントID
     */
    function updateValidatorAccountId(bytes32 validatorId, bytes32 validatorAccountId) external;

    /**
     * @dev 検証者にアカウントIDを追加する
     * @param validatorId 検証者ID
     * @param accountId 追加するアカウントID
     */
    function addAccountIdToValidator(bytes32 validatorId, bytes32 accountId) external;

    ///////////////////////////////////
    // ValidatorIds配列操作
    ///////////////////////////////////

    /**
     * @dev 検証者IDを配列に追加する
     * @param validatorId 追加する検証者ID
     */
    function addValidatorId(bytes32 validatorId) external;

    /**
     * @dev 検証者IDの配列を取得する
     * @return validatorIds 検証者IDの配列
     */
    function getValidatorIds() external view returns (bytes32[] memory validatorIds);

    /**
     * @dev 検証者IDを指定インデックスで取得する
     * @param index インデックス
     * @return validatorId 検証者ID
     */
    function getValidatorIdByIndex(uint256 index) external view returns (bytes32 validatorId);

    /**
     * @dev 検証者IDの総数を取得する
     * @return count 検証者IDの総数
     */
    function getValidatorIdsCount() external view returns (uint256 count);

    ///////////////////////////////////
    // ValidatorIdExistence マッピング操作
    ///////////////////////////////////

    /**
     * @dev 検証者IDの存在フラグを取得する
     * @param validatorId 検証者ID
     * @return exists 存在フラグ
     */
    function getValidatorIdExistence(bytes32 validatorId) external view returns (bool exists);

    /**
     * @dev 検証者IDの存在フラグを設定する
     * @param validatorId 検証者ID
     * @param exists 存在フラグ
     */
    function setValidatorIdExistence(bytes32 validatorId, bool exists) external;

    ///////////////////////////////////
    // AccountIdExistenceByValidatorId マッピング操作
    ///////////////////////////////////

    /**
     * @dev 検証者IDに紐づくアカウントIDの存在フラグを取得する
     * @param validatorId 検証者ID
     * @param accountId アカウントID
     * @return exists 存在フラグ
     */
    function getAccountIdExistenceByValidatorId(bytes32 validatorId, bytes32 accountId)
        external
        view
        returns (bool exists);

    /**
     * @dev 検証者IDに紐づくアカウントIDの存在フラグを設定する
     * @param validatorId 検証者ID
     * @param accountId アカウントID
     * @param exists 存在フラグ
     */
    function setAccountIdExistenceByValidatorId(
        bytes32 validatorId,
        bytes32 accountId,
        bool exists
    ) external;

    ///////////////////////////////////
    // IssuerIdLinkedFlag マッピング操作
    ///////////////////////////////////

    /**
     * @dev 発行者IDの紐付けフラグを取得する
     * @param issuerId 発行者ID
     * @return linked 紐付けフラグ
     */
    function getIssuerIdLinkedFlag(bytes32 issuerId) external view returns (bool linked);

    /**
     * @dev 発行者IDの紐付けフラグを設定する
     * @param issuerId 発行者ID
     * @param linked 紐付けフラグ
     */
    function setIssuerIdLinkedFlag(bytes32 issuerId, bool linked) external;

    ///////////////////////////////////
    // バックアップ・リストア関連
    ///////////////////////////////////

    /**
     * @dev バックアップ用に全検証者データを設定する（Admin権限必要）
     * @param validator 全検証者データ
     * @param deadline 署名の期限
     * @param signature Admin署名
     */
    function setValidatorAll(
        ValidatorAll memory validator,
        uint256 deadline,
        bytes memory signature
    ) external;

    /**
     * @dev バックアップ用に検証者データを取得する
     * @param index インデックス
     * @return validator 全検証者データ
     */
    function getValidatorAll(uint256 index) external view returns (ValidatorAll memory validator);

    /**
     * @dev LogicコントラクトのアドレスをContractManagerアドレスに変更する（Admin権限必要）
     * @param contractManagerAddr 新しいContractManagerアドレス
     */
    function setContractManagerAddress(address contractManagerAddr) external;

    /**
     * @dev ContractManagerアドレスを取得する
     * @return contractManagerAddr ContractManagerアドレス
     */
    function getContractManagerAddress() external view returns (address contractManagerAddr);
}
