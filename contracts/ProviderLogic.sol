// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "./interfaces/IContractManager.sol";
import "./interfaces/IProviderStorage.sol";
import "./interfaces/IProvider.sol";
import "./interfaces/Error.sol";
import "./remigration/RemigrationLib.sol";
import "./libraries/ProviderLogicCallLib.sol";
import "./libraries/ProviderLogicExecuteLib.sol";
import {ProviderData, ZoneData, ProviderAll} from "./interfaces/Struct.sol";

/**
 * @dev Providerコントラクト
 */
contract ProviderLogic is Initializable, IProvider {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev ProviderStorageアドレス */
    IProviderStorage private _providerStorage;

    /* @dev setProviderAllのsignature検証用 */
    string private constant SET_PROVIDER_ALL_SIGNATURE = "setProviderAll";

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 初期化関数
     *
     * @param contractManager ContractManagerアドレス
     * @param providerStorage ProviderStorageコントラクトアドレス
     */
    function initialize(IContractManager contractManager, IProviderStorage providerStorage)
        public
        initializer
    {
        require(
            address(contractManager) != address(0) && address(providerStorage) != address(0),
            Error.RV0003_PROV_INVALID_VAL
        );
        _contractManager = contractManager;
        _providerStorage = providerStorage;
    }

    /**
     * @dev コントラクトバージョン取得。
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // verify sender functions
    ///////////////////////////////////

    /**
     * @dev Admin権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(内部関数)。
     *
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     */
    function _adminOnly(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) internal view {
        (bool has, string memory err) = ProviderLogicCallLib.hasAdminRole(
            _contractManager,
            hash,
            deadline,
            signature
        );
        require(
            bytes(err).length == 0 && has,
            bytes(err).length > 0 ? err : Error.GA0006_PROV_NOT_ADMIN_ROLE
        );
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev Providerの追加。Adminの権限が必要。
     *
     * ```
     * emit event: AddProvider()
     * ```
     *
     * @param providerId providerID
     * @param zoneId zoneId
     * @param zoneName zoneName
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addProvider(
        bytes32 providerId,
        uint16 zoneId,
        string memory zoneName,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        _adminOnly(keccak256(abi.encode(providerId, zoneId, deadline)), deadline, signature);

        // provider未登録チェック
        ProviderLogicCallLib.checkAddProviderIsValid(_providerStorage, providerId);

        // Execute adding provider
        ProviderLogicExecuteLib.executeAddProvider(
            _providerStorage,
            _contractManager,
            providerId,
            zoneId,
            zoneName
        );

        // Emit event
        emit AddProvider(providerId, zoneId, zoneName, traceId);
    }

    /**
     * @dev (Fin専用)Finで管理を行うZone情報の追加。Adminの権限が必要。
     *
     * ```
     * emit event: AddBizZone()
     * ```
     *
     * @param zoneId zoneId
     * @param zoneName zoneName
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addBizZone(
        uint16 zoneId,
        string memory zoneName,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        _adminOnly(keccak256(abi.encode(zoneId, zoneName, deadline)), deadline, signature);

        // provider登録チェック
        ProviderLogicCallLib.validateProviderExists(_providerStorage);

        // Execute adding biz zone
        ProviderLogicExecuteLib.executeAddBizZone(_providerStorage, zoneId, zoneName);

        // Emit event
        emit AddBizZone(zoneId, zoneName);
    }

    /**
     * @dev Provider権限の追加。Adminの権限が必要。
     *
     * ```
     * emit event: AddProviderRole()
     * ```
     *
     * @param providerId providerID
     * @param providerEoa providerEoa
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addProviderRole(
        bytes32 providerId,
        address providerEoa,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        _adminOnly(keccak256(abi.encode(providerId, providerEoa, deadline)), deadline, signature);

        // providerId有効性チェック
        ProviderLogicCallLib.checkAddProviderRoleIsValid(_providerStorage, providerId, providerEoa);

        // Add role
        ProviderData memory providerData = _providerStorage.getProviderData(providerId);
        _contractManager.accessCtrl().addRoleByProv(providerId, providerData.role, providerEoa);

        // Emit event
        emit AddProviderRole(providerId, providerEoa, traceId);
    }

    /**
     * @dev Provider名の更新。Adminの権限が必要。
     *
     * ```
     * emit event: ModProvider()
     * ```
     *
     * @param providerId providerID
     * @param name 更新後のproviderの名前
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function modProvider(
        bytes32 providerId,
        bytes32 name,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        _adminOnly(keccak256(abi.encode(providerId, name, deadline)), deadline, signature);

        // providerId有効性チェック
        ProviderLogicCallLib.checkHasProvider(_providerStorage, providerId);

        // name 更新後のproviderの名前
        ProviderLogicExecuteLib.updateProviderName(_providerStorage, providerId, name);

        // Emit event
        emit ModProvider(providerId, name, traceId);
    }

    /**
     * @dev Zone名の更新。Adminの権限が必要。
     *
     * ```
     * emit event: ModProvider()
     * ```
     *
     * @param providerId providerID
     * @param zoneName 更新後のzone名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function modZone(
        bytes32 providerId,
        string memory zoneName,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external {
        // Admin権限を持つかチェック
        _adminOnly(keccak256(abi.encode(providerId, deadline)), deadline, signature);

        // providerId有効性チェック
        ProviderLogicCallLib.checkHasProvider(_providerStorage, providerId);

        // zoneName 更新後のzone名
        ProviderLogicExecuteLib.updateZoneName(_providerStorage, providerId, zoneName);

        // Emit event
        emit ModZone(providerId, zoneName, traceId);
    }

    /**
     * @dev Tokenの追加。
     *
     * ```
     * emit event: AddTokenByProvider()
     * ```
     *
     * @param providerId providerID
     * @param tokenId tokenId
     * @param name トークン名
     * @param symbol symbol
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addToken(
        bytes32 providerId,
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // token登録チェック
        ProviderLogicCallLib.checkAddTokenIsValid(
            _contractManager,
            _providerStorage,
            providerId,
            tokenId,
            deadline,
            signature,
            name,
            symbol
        );

        // Add token
        _contractManager.token().addToken(tokenId, name, symbol, traceId);

        // Emit event
        emit AddTokenByProvider(providerId, tokenId, traceId);
    }

    /**
     * @dev Tokenのステータス変更。name, symbolのみ変更を許可し、空を許容しない。
     *
     * @param tokenId tokenId
     * @param name トークン名
     * @param symbol symbol
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function modToken(
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // token登録チェック
        ProviderLogicCallLib.checkModTokenIsValid(
            _contractManager,
            _providerStorage,
            tokenId,
            deadline,
            signature,
            name,
            symbol
        );

        // Update token
        _contractManager.token().modToken(tokenId, name, symbol, traceId);
    }

    /**
     * @dev Biz ZoneとIssuerの紐付け。
     *
     * @param issuerId issuerId
     * @param zoneId zoneId
     */
    function addBizZoneToIssuer(bytes32 issuerId, uint16 zoneId) external override {
        // addBizZoneToIssuerの検証処理
        ProviderLogicCallLib.checkAddBizZoneToIssuerIsValid(_providerStorage, issuerId, zoneId);

        // ゾーンとIssuerの紐付け処理を実行する
        ProviderLogicExecuteLib.executeAddBizZoneToIssuer(_providerStorage, zoneId, issuerId);
    }

    /**
     * @dev Biz ZoneとIssuerの紐付け削除。
     *
     * @param issuerId issuerId
     * @param zoneId zoneId
     */
    function deleteBizZoneToIssuer(bytes32 issuerId, uint16 zoneId) external override {
        // deleteBizZoneToIssuerの検証処理
        ProviderLogicCallLib.checkDeleteBizZoneToIssuerIsValid(_providerStorage, zoneId);

        // ゾーンとIssuerの紐付け削除処理を実行する
        ProviderLogicExecuteLib.executeDeleteBizZoneToIssuer(_providerStorage, zoneId, issuerId);
    }

    /**
     * @dev プロバイダ全情報登録
     * @param provider 全プロバイダの情報
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setProviderAll(
        ProviderAll memory provider,
        uint256 deadline,
        bytes memory signature
    ) external {
        // Admin権限を持つかチェック
        _adminOnly(
            keccak256(abi.encode(SET_PROVIDER_ALL_SIGNATURE, deadline)),
            deadline,
            signature
        );

        // バックアップ用に全プロバイダデータを設定する
        ProviderLogicExecuteLib.setProviderAll(_providerStorage, provider, deadline, signature);
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev Providerの存在確認。
     *
     * @param providerId providerID
     * @return success true:Providerが存在する,false:Providerが存在しない
     * @return err エラーメッセージ
     */
    function hasProvider(bytes32 providerId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return ProviderLogicCallLib.hasProvider(_providerStorage, providerId);
    }

    /**
     * @dev Tokenの存在確認。
     *
     * @param tokenId tokenId
     * @param providerId providerId
     * @param chkEnabled true:有効性確認を行う,false:有効性確認を行わない
     * @return success true:Tokenが存在し、有効,false:Tokenが存在しない、あるいは無効
     * @return err エラーメッセージ
     */
    function hasToken(
        bytes32 tokenId,
        bytes32 providerId,
        bool chkEnabled
    ) external view returns (bool success, string memory err) {
        return
            ProviderLogicCallLib.hasToken(
                _providerStorage,
                _contractManager,
                tokenId,
                providerId,
                chkEnabled
            );
    }

    /**
     * @dev Provider情報の取得。
     *
     * @return providerId providerId
     * @return zoneId zoneId
     * @return zoneName zoneName
     * @return err エラーメッセージ
     */
    function getProvider()
        external
        view
        override
        returns (
            bytes32 providerId,
            uint16 zoneId,
            string memory zoneName,
            string memory err
        )
    {
        return ProviderLogicCallLib.getProviderInfo(_providerStorage);
    }

    /**
     * @dev zoneIDの取得。
     *
     * @return zoneId zoneID
     * @return zoneName zoneName
     * @return err エラーメッセージ
     */
    function getZone()
        external
        view
        override
        returns (
            uint16 zoneId,
            string memory zoneName,
            string memory err
        )
    {
        return ProviderLogicCallLib.getZone(_providerStorage);
    }

    /**
     * @dev zone名称の取得。
     *
     * @return zoneName zoneName
     */
    function getZoneName(uint16 zoneId) external view override returns (string memory zoneName) {
        return ProviderLogicCallLib.getZoneName(_providerStorage, zoneId);
    }

    /**
     * @dev TokenIDの取得。
     *
     * @return tokenId tokenID
     * @return err エラーメッセージ
     */
    function getTokenId() external view override returns (bytes32 tokenId, string memory err) {
        (tokenId, , , , , err) = _contractManager.token().getToken();
        return (tokenId, err);
    }

    /**
     * @dev Token情報の取得。
     *
     * @param providerId providerID
     * @return tokenId tokenId
     * @return name トークン名
     * @return symbol symbol
     * @return totalSupply 合計供給量
     * @return enabled true:Tokenが有効, false:Tokenが無効
     * @return err エラーメッセージ
     */
    function getToken(bytes32 providerId)
        external
        view
        override
        returns (
            bytes32 tokenId,
            bytes32 name,
            bytes32 symbol,
            uint256 totalSupply,
            bool enabled,
            string memory err
        )
    {
        // providerId有効性チェック
        (bool success, string memory errTmp) = ProviderLogicCallLib.hasProvider(
            _providerStorage,
            providerId
        );
        if (!success) {
            return (0x00, 0x00, 0x00, 0, false, errTmp);
        }
        return _contractManager.token().getToken();
    }

    /**
     * @dev Provider数の取得。
     *
     * @return count providerの数
     */
    function getProviderCount() external view override returns (uint256 count) {
        return _providerStorage.getProviderCount();
    }

    /**
     * @dev 権限の確認。
     *
     * @param providerId providerID
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     * @return has true:ロールを所持,false:ロールを不所持
     * @return err エラーメッセージ
     */
    function checkRole(
        bytes32 providerId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view override returns (bool has, string memory err) {
        return
            ProviderLogicCallLib.hasProviderRole(
                _contractManager,
                _providerStorage,
                providerId,
                hash,
                deadline,
                signature
            );
    }

    /**
     * @dev 認可イシュアを取得。
     *
     * @param zoneId zoneId
     * @return availableIssuerIds 認可イシュアリスト
     */
    function getAvailableIssuerIds(uint16 zoneId)
        external
        view
        override
        returns (bytes32[] memory availableIssuerIds)
    {
        return _providerStorage.getAvailableIssuerIds(zoneId);
    }

    /**
     * @dev 認可イシュアを確認。
     *
     * @param zoneId zoneId
     * @param accountId accountId
     * @return success 成功したかどうか
     * @return err エラーメッセージ
     */
    function checkAvailableIssuerIds(uint16 zoneId, bytes32 accountId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return
            ProviderLogicCallLib.checkAvailableIssuerIds(
                _providerStorage,
                _contractManager,
                zoneId,
                accountId
            );
    }

    /**
     * @dev プロバイダ全情報取得
     *      既に登録されているプロバイダの全情報を取得する
     */
    function getProviderAll(bytes32 providerId)
        external
        view
        returns (ProviderAll memory provider)
    {
        return ProviderLogicCallLib.getProviderAll(_providerStorage, providerId);
    }

    ///////////////////////////////////
    // for upgrade contracts
    // deprecated
    ///////////////////////////////////

    // uint256[50] private __gap;
}
