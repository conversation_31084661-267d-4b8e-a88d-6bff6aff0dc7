// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import "./interfaces/IContractManager.sol";
import "./interfaces/ITokenStorage.sol";
import "./interfaces/IToken.sol";
import "./interfaces/ITransferable.sol";
import "./interfaces/Error.sol";

// ライブラリのimport
import "./libraries/TokenLogicCallLib.sol";
import "./libraries/TokenLogicExecuteLib.sol";
import "./interfaces/Struct.sol";

/**
 * @dev Tokenコントラクト
 */
contract TokenLogic is Initializable, IToken, ITransferable {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev TokenStorageアドレス */
    ITokenStorage private _tokenStorage;

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 初期化関数
     *
     * @param contractManager ContractManagerアドレス
     * @param tokenStorage TokenStorageアドレス
     */
    function initialize(IContractManager contractManager, ITokenStorage tokenStorage)
        public
        initializer
    {
        require(
            address(contractManager) != address(0) && address(tokenStorage) != address(0),
            Error.RV0006_ISSUER_INVALID_VAL
        );
        _contractManager = contractManager;
        _tokenStorage = tokenStorage;
    }

    /**
     * @dev コントラクトバージョン取得。
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev Providerから呼ばれるaddToken。
     *
     * ```
     * emit event: AddToken()
     * ```
     *
     * @param tokenId tokenId
     * @param name Tokenの名前
     * @param symbol symbol
     * @param traceId トレースID
     */
    function addToken(
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol,
        bytes32 traceId
    ) external override {
        TokenLogicCallLib.addTokenIsValid(_contractManager, _tokenStorage, tokenId, msg.sender);
        TokenLogicExecuteLib.executeAddToken(_tokenStorage, tokenId, name, symbol);

        (uint16 zoneId, string memory zoneName, string memory errZone) = _contractManager
            .provider()
            .getZone();
        require(bytes(errZone).length == 0, errZone);

        emit AddToken(tokenId, zoneId, zoneName, true, traceId);
    }

    /**
     * @dev Tokenの修正。名前、symbol、peg通貨の種類を修正できる。
     *
     * ```
     * emit event: ModToken()
     * ```
     *
     * @param tokenId tokenId
     * @param name Tokenの名前
     * @param symbol zoneId
     * @param traceId トレースID
     */
    function modToken(
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol,
        bytes32 traceId
    ) external override {
        TokenLogicCallLib.modTokenIsValid(_contractManager, _tokenStorage, tokenId, msg.sender);

        TokenLogicExecuteLib.executeModToken(_tokenStorage, tokenId, name, symbol);

        (name, symbol, , , ) = TokenLogicCallLib.getToken(_tokenStorage, tokenId);
        emit ModToken(tokenId, name, symbol, traceId);
    }

    /**
     * @dev Tokenのステータスを変更する。
     *
     * ```
     * emit event: SetEnabledToken()
     * ```
     *
     * @param providerId providerID
     * @param tokenId tokenId
     * @param enabled Tokenの有効性.true:有効,false:無効
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function setTokenEnabled(
        bytes32 providerId,
        bytes32 tokenId,
        bool enabled,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        TokenLogicCallLib.setTokenEnabledIsValid(
            _contractManager,
            _tokenStorage,
            providerId,
            tokenId,
            enabled,
            deadline,
            signature
        );
        TokenLogicExecuteLib.executeSetTokenEnabled(_tokenStorage, tokenId, enabled);
        emit SetEnabledToken(tokenId, enabled, traceId);
    }

    /**
     * @dev 送金許可。
     *
     * ```
     * emit event: Approval()
     * ```
     * @param validatorId ownerId
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @param amount 送金数量
     * @param traceId トレースID
     */
    function approve(
        bytes32 validatorId,
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        TokenLogicCallLib.hasValidator(_contractManager, validatorId);

        //Accountにてapproveを実行する
        _contractManager.account().approve(ownerId, spenderId, amount);

        emit Approval(validatorId, ownerId, spenderId, amount, traceId);
    }

    /**
     * @dev 発行。
     *
     * ```
     * emit event: Mint()
     * ```
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param amount Mintする数量
     * @param traceId トレースID
     */
    function mint(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        TokenLogicCallLib.mintIsValid(issuerId, accountId);

        (
            uint16 zoneId,
            bytes32 validatorId,
            string memory accountName,
            uint256 balance
        ) = TokenLogicExecuteLib.executeMint(
                _contractManager,
                _tokenStorage,
                accountId,
                amount,
                traceId
            );

        emit Mint(zoneId, validatorId, issuerId, accountId, accountName, amount, balance, traceId);

        // 取引後残高連携
        _contractManager.account().emitAfterBalance(accountId, Constant._EMPTY_VALUE, traceId);
    }

    /**
     * @dev 償却。
     *
     * ```
     * emit event: Burn()
     * ```
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param amount Burnする数量
     * @param traceId トレースID
     */
    function burn(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        TokenLogicCallLib.burnIsValid(issuerId, accountId);
        (
            uint16 zoneId,
            bytes32 validatorId,
            string memory accountName,
            uint256 balance
        ) = TokenLogicExecuteLib.executeBurn(
                _contractManager,
                _tokenStorage,
                accountId,
                amount,
                traceId
            );

        emit Burn(zoneId, validatorId, issuerId, accountId, accountName, amount, balance, traceId);

        // 取引後残高連携
        _contractManager.account().emitAfterBalance(accountId, Constant._EMPTY_VALUE, traceId);
    }

    /**
     * @dev 償却取り消し。
     *
     * ```
     * emit event: BurnCancel()
     * ```
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param amount Burnを取り消す数量
     * @param blockTimestamp 取り消し対象のBurnの日時
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function burnCancel(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        uint256 blockTimestamp,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        BurnCancelData memory burnCancelData = BurnCancelData({
            tokenId: TokenLogicCallLib.getTokenId(_tokenStorage),
            issuerId: issuerId,
            accountId: accountId,
            amount: amount,
            blockTimestamp: blockTimestamp,
            deadline: deadline,
            signature: signature
        });

        TokenLogicCallLib.burnCancelIsValid(_contractManager, _tokenStorage, burnCancelData);
        _burnCancel(burnCancelData, traceId);
    }

    /**
     * @dev 償却取り消し処理
     *
     * @param burnCancelData 償却取り消しデータ
     * @param traceId トレースID
     */
    function _burnCancel(BurnCancelData memory burnCancelData, bytes32 traceId) internal {
        // Tokenの償却取り消しを行い、結果を保存(ライブラリ側で実行)
        (uint16 zoneId, bytes32 validatorId, uint256 balance) = TokenLogicExecuteLib
            .executeBurnCancel(_contractManager, _tokenStorage, burnCancelData);

        // Account already check in logic, remove duplicate check of get account to prevent stack too deep
        (AccountDataWithoutZoneId memory accountData, ) = _contractManager.account().getAccount(
            burnCancelData.accountId
        );

        emit BurnCancel(
            zoneId,
            validatorId,
            burnCancelData.issuerId,
            burnCancelData.accountId,
            accountData.accountName,
            burnCancelData.amount,
            balance,
            burnCancelData.blockTimestamp,
            traceId
        );
    }

    /**
     * @dev 送金許可設定の取得 Callで呼ばれる時にValidatorの紐付き検証を行う。
     *
     * @param validatorId validatorId
     * @param ownerId ownerId
     * @param spenderId spenderId
     * @return allowance 許容額
     * @return approvedAt 許可日付
     * @return err エラーメッセージ
     */
    function getAllowance(
        bytes32 validatorId,
        bytes32 ownerId,
        bytes32 spenderId
    )
        external
        view
        override
        returns (
            uint256 allowance,
            uint256 approvedAt,
            string memory err
        )
    {
        return TokenLogicCallLib.getAllowance(_contractManager, validatorId, ownerId, spenderId);
    }

    /**
     * @dev 送金許可一覧照会 TODO:Core APIとのマッピング時に作成
     *
     * @param ownerAccountId 送金許可元ID
     * @param offset オフセット
     * @param limit リミット
     * @return approvalData 送金許可設定一覧
     * @return totalCount 総数
     * @return err エラーメッセージ
     */
    function getAllowanceList(
        bytes32 ownerAccountId,
        uint256 offset,
        uint256 limit
    )
        external
        view
        override
        returns (
            AccountApprovalAll[] memory approvalData,
            uint256 totalCount,
            string memory err
        )
    {
        return (_contractManager.account().getAllowanceList(ownerAccountId, offset, limit));
    }

    /**
     * @dev 単数のTransferを行う。
     *
     * @param sendAccountId sendAccountId
     * @param fromAccountId fromAccountId
     * @param toAccountId toAccountId
     * @param amount 送金額
     * @param miscValue1 カスタムコントラクト用パラメータ1
     * @param miscValue2 カスタムコントラクト用パラメータ2
     * @param traceId トレースID
     */
    function transferSingle(
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        string memory memo,
        bytes32 traceId
    ) external override {
        TransferData memory emitData = TokenLogicExecuteLib.executeTransferSingle(
            _contractManager,
            sendAccountId,
            fromAccountId,
            toAccountId,
            amount,
            miscValue1,
            miscValue2,
            memo,
            traceId
        );
        emit Transfer(emitData, traceId);

        // 取引後残高連携
        _contractManager.account().emitAfterBalance(fromAccountId, toAccountId, traceId);
    }

    /**
     * @dev TotalSupplyを増額する。
     *
     * @param amount Mintする数量
     */
    function addTotalSupply(uint256 amount) external override {
        TokenLogicExecuteLib.executeAddTotalSupply(_tokenStorage, amount);
    }

    /**
     * @dev TotalSupplyを減額する。
     *
     * @param amount Burnする数量
     */
    function subTotalSupply(uint256 amount) external override {
        TokenLogicExecuteLib.executeSubTotalSupply(_tokenStorage, amount);
    }

    /**
     * @dev カスタムコントラクトから呼び出す為のCustomTransfer。
     *
     * @param sendAccountId sendAccountId
     * @param fromAccountId fromAccountId
     * @param toAccountId toAccountId
     * @param amount 送金額
     * @param miscValue1 カスタムコントラクト用パラメータ1
     * @param miscValue2 カスタムコントラクト用パラメータ2
     * @param traceId トレースID
     * @return result true:成功,false:失敗
     */
    function customTransfer(
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        string memory memo,
        bytes32 traceId
    ) external override returns (bool result) {
        TransferData memory data;
        {
            data.transferType = Constant._CUSTOM_TRANSFER;
            data.bizZoneId = 0;
            data.sendAccountId = sendAccountId;
            data.fromAccountId = fromAccountId;
            data.toAccountId = toAccountId;
            data.amount = amount;
            data.miscValue1 = miscValue1;
            data.miscValue2 = miscValue2;
            data.memo = memo;
        }
        TransferData memory emitData = TokenLogicExecuteLib.transfer(_contractManager, data);

        emit Transfer(emitData, traceId);

        // 取引後残高連携
        _contractManager.account().emitAfterBalance(fromAccountId, toAccountId, traceId);

        return true;
    }

    /**
     * @dev トークン全情報登録
     * @param token Tokenの情報
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setTokenAll(
        TokenAll memory token,
        uint256 deadline,
        bytes memory signature
    ) external {
        TokenLogicExecuteLib.executeSetTokenAll(_tokenStorage, token, deadline, signature);
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev TokenId確認用。
     *
     * @param tokenId tokenId
     * @param chkEnabled true:有効性確認を行う,false:有効性確認を行わない
     * @return success true:Tokenが存在し,false:Tokenが存在しない
     * @return err エラーメッセージ
     */
    function hasToken(bytes32 tokenId, bool chkEnabled)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return
            TokenLogicCallLib.hasToken(
                _tokenStorage,
                TokenLogicCallLib.getTokenId(_tokenStorage),
                tokenId,
                chkEnabled
            );
    }

    /**
     * @dev 内部管理TokenId確認用。
     *
     * @return success true:Tokenが存在し,false:Tokenが存在しない
     * @return err エラーメッセージ
     */
    function hasTokenState() external view override returns (bool success, string memory err) {
        return
            TokenLogicCallLib.hasToken(
                _tokenStorage,
                TokenLogicCallLib.getTokenId(_tokenStorage),
                TokenLogicCallLib.getTokenId(_tokenStorage),
                true
            );
    }

    /**
     * @dev Token情報の取得。
     *
     * @return tokenId tokenId
     * @return name token名
     * @return symbol symbol
     * @return totalSupply tokenの総供給量
     * @return enabled ture:有効,false:無効
     * @return err エラーメッセージ
     */
    function getToken()
        external
        view
        override
        returns (
            bytes32 tokenId,
            bytes32 name,
            bytes32 symbol,
            uint256 totalSupply,
            bool enabled,
            string memory err
        )
    {
        TokenLogicCallLib.getTokenIsValid(_contractManager, msg.sender);
        return TokenLogicCallLib.getToken(_tokenStorage);
    }

    /**
     * @dev ビジネスゾーンの全残高情報を取得する
     *
     * @param accountId accountId
     * @return zoneIds ビジネスゾーンID
     * @return zoneNames ゾーン名
     * @return balances 残高
     * @return accountNames アカウント名
     * @return accountStatus アカウントステータス
     * @return totalBalance 合計残高
     * @return err エラーメッセージ
     */
    function getBalanceList(bytes32 accountId)
        external
        view
        returns (
            uint16[] memory zoneIds,
            string[] memory zoneNames,
            uint256[] memory balances,
            string[] memory accountNames,
            bytes32[] memory accountStatus,
            uint256 totalBalance,
            string memory err
        )
    {
        return TokenLogicCallLib.getBalanceList(_contractManager, accountId);
    }

    function checkApprove(
        bytes32 validatorId,
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount,
        bytes memory accountSignature,
        bytes memory info,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool success, string memory err) {
        return
            TokenLogicCallLib.checkApprove(
                _contractManager,
                validatorId,
                ownerId,
                spenderId,
                amount,
                accountSignature,
                info,
                deadline,
                signature
            );
    }

    /**
     * @dev Token全情報取得
     *      既に登録されているTokenの全情報を取得する
     *
     * @return token 全Tokenの情報
     */
    function getTokenAll() external view returns (TokenAll memory token) {
        return TokenLogicCallLib.getTokenAll(_tokenStorage);
    }
}
