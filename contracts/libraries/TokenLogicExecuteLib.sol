// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "@openzeppelin/contracts-upgradeable/utils/math/SafeMathUpgradeable.sol";
import "../interfaces/IContractManager.sol";
import "../interfaces/ITokenStorage.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

/**
 * @dev TokenLogicExecuteLibライブラリ
 *      Tokenの実行関数を実装するヘルパーライブラリ
 */
library TokenLogicExecuteLib {
    using SafeMathUpgradeable for uint256;
    /** @dev FinZoneのID */
    uint16 private constant FINANCIAL_ZONE = 3000;
    /** @dev  IBC検証用のtimeoutHeightの値*/
    uint64 private constant TIMEOUT_HEIGHT = 9999999999999999999;
    /** @dev パラメータの検証に使用する固定値 **/
    bytes32 private constant EMPTY_BYTES32 = 0x00;

    /**
     * @dev Tokenの追加処理を実行する
     *
     * @param tokenStorage TokenStorage参照
     * @param tokenId tokenId
     * @param name Tokenの名前
     * @param symbol symbol
     */
    function executeAddToken(
        ITokenStorage tokenStorage,
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol
    ) external {
        TokenData memory tokenData = TokenData({
            name: name,
            symbol: symbol,
            totalSupply: 0,
            enabled: true
        });
        tokenStorage.setTokenData(tokenId, tokenData);
        tokenStorage.setTokenId(tokenId);
    }

    /**
     * @dev Tokenの修正処理を実行する
     *
     * @param tokenStorage TokenStorage参照
     * @param tokenId tokenId
     * @param name Tokenの名前
     * @param symbol symbol
     */
    function executeModToken(
        ITokenStorage tokenStorage,
        bytes32 tokenId,
        bytes32 name,
        bytes32 symbol
    ) external {
        tokenStorage.updateToken(tokenId, name, symbol);
    }

    /**
     * @dev Tokenの有効性設定処理を実行する
     *
     * @param tokenStorage TokenStorage参照
     * @param tokenId tokenId
     * @param enabled 有効性フラグ
     */
    function executeSetTokenEnabled(
        ITokenStorage tokenStorage,
        bytes32 tokenId,
        bool enabled
    ) external {
        tokenStorage.setTokenEnabled(tokenId, enabled);
    }

    /**
     * @dev 発行処理を実行する
     *
     * @param contractManager ContractManager参照
     * @param tokenStorage TokenStorage参照
     * @param accountId accountId
     * @param amount Mintする数量
     * @param traceId トレースID
     * @return zoneId ゾーンID
     * @return validatorId バリデーターID
     * @return accountName アカウント名
     * @return balance 残高
     */
    function executeMint(
        IContractManager contractManager,
        ITokenStorage tokenStorage,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    )
        external
        returns (
            uint16 zoneId,
            bytes32 validatorId,
            string memory accountName,
            uint256 balance
        )
    {
        // zoneIdの取得
        (zoneId, , ) = contractManager.provider().getZone();

        tokenStorage.addTotalSupply(tokenStorage.getTokenId(), amount);

        //Account増額
        balance = contractManager.account().mint(accountId, amount);

        //Accountに紐づくvalidatorIdを取得(bcmonitoringのフィルタリング用)
        (validatorId, ) = contractManager.account().getValidatorIdByAccountId(accountId);

        //Account名義返却のため、アカウント情報を取得
        (AccountDataWithoutZoneId memory accountData, ) = contractManager.account().getAccount(
            accountId
        );

        // アカウントの限度額更新
        contractManager.financialZoneAccount().syncMint(accountId, amount, traceId);

        return (zoneId, validatorId, accountData.accountName, balance);
    }

    /**
     * @dev 償却処理を実行する
     *
     * @param contractManager ContractManager参照
     * @param tokenStorage TokenStorage参照
     * @param accountId accountId
     * @param amount Burnする数量
     * @param traceId トレースID
     * @return zoneId ゾーンID
     * @return validatorId バリデーターID
     * @return accountName アカウント名
     * @return balance 残高
     */
    function executeBurn(
        IContractManager contractManager,
        ITokenStorage tokenStorage,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    )
        external
        returns (
            uint16 zoneId,
            bytes32 validatorId,
            string memory accountName,
            uint256 balance
        )
    {
        // zoneIdの取得
        (zoneId, , ) = contractManager.provider().getZone();

        //Account減額
        balance = contractManager.account().burn(accountId, amount);

        //TotalSupply減額
        tokenStorage.subTotalSupply(tokenStorage.getTokenId(), amount);

        //Accountに紐づくvalidatorIdを取得(bcmonitoringのフィルタリング用)
        (validatorId, ) = contractManager.account().getValidatorIdByAccountId(accountId);

        //Account名義返却のため、アカウント情報を取得
        (AccountDataWithoutZoneId memory accountData, ) = contractManager.account().getAccount(
            accountId
        );

        // アカウントの限度額更新
        contractManager.financialZoneAccount().syncBurn(accountId, amount, traceId);

        return (zoneId, validatorId, accountData.accountName, balance);
    }

    /**
     * @dev 償却取り消し処理を実行する
     *
     * @param contractManager ContractManager参照
     * @param tokenStorage TokenStorage参照
     * @param burnCancelData 償却取り消しデータ
     * @return zoneId ゾーンID
     * @return validatorId バリデーターID
     * @return balance 残高
     */
    function executeBurnCancel(
        IContractManager contractManager,
        ITokenStorage tokenStorage,
        BurnCancelData memory burnCancelData
    )
        external
        returns (
            uint16 zoneId,
            bytes32 validatorId,
            uint256 balance
        )
    {
        // zoneIdの取得
        (zoneId, , ) = contractManager.provider().getZone();

        //TotalSupply増額
        tokenStorage.addTotalSupply(tokenStorage.getTokenId(), burnCancelData.amount);

        // 指定された数量のBurnをCancelする
        balance = contractManager.account().mint(burnCancelData.accountId, burnCancelData.amount);

        //Accountに紐づくvalidatorIdを取得(bcmonitoringのフィルタリング用)
        (validatorId, ) = contractManager.account().getValidatorIdByAccountId(
            burnCancelData.accountId
        );

        return (zoneId, validatorId, balance);
    }

    /**
     * @dev TotalSupply増額の実行
     *
     * @param tokenStorage TokenStorage参照
     * @param amount 増額する数量
     */
    function executeAddTotalSupply(ITokenStorage tokenStorage, uint256 amount) external {
        tokenStorage.addTotalSupply(tokenStorage.getTokenId(), amount);
    }

    /**
     * @dev TotalSupply減額の実行
     *
     * @param tokenStorage TokenStorage参照
     * @param amount 減額する数量
     */
    function executeSubTotalSupply(ITokenStorage tokenStorage, uint256 amount) external {
        tokenStorage.subTotalSupply(tokenStorage.getTokenId(), amount);
    }

    /**
     * @dev Token全情報設定の実行
     *
     * @param tokenStorage TokenStorage参照
     * @param token 設定するToken情報
     * @param deadline 署名の期限
     * @param signature 署名
     */
    function executeSetTokenAll(
        ITokenStorage tokenStorage,
        TokenAll memory token,
        uint256 deadline,
        bytes memory signature
    ) external {
        tokenStorage.setTokenAll(token, deadline, signature);
    }

    /**
     * @dev 単一送金処理を実行する
     *
     * @param contractManager ContractManager参照
     * @param sendAccountId 送信アカウントID
     * @param fromAccountId 送信元アカウントID
     * @param toAccountId 送信先アカウントID
     * @param amount 送金額
     * @param miscValue1 その他値1
     * @param miscValue2 その他値2
     * @param memo メモ
     * @param traceId トレースID
     * @return emitData 送金データ
     */
    function executeTransferSingle(
        IContractManager contractManager,
        bytes32 sendAccountId,
        bytes32 fromAccountId,
        bytes32 toAccountId,
        uint256 amount,
        bytes32 miscValue1,
        string memory miscValue2,
        string memory memo,
        bytes32 traceId
    ) external returns (TransferData memory emitData) {
        (uint16 zoneId, , ) = contractManager.provider().getZone();
        if (zoneId == FINANCIAL_ZONE) {
            // 実行箇所がFinZoneであればアカウントの限度額更新(BizZone実行の場合はIBCの伝播先で更新する)
            contractManager.financialZoneAccount().syncTransfer(fromAccountId, amount, traceId);
        } else {
            // 実行箇所がBizZoneであればsyncTransferを呼び出してpacketをFinZoneに送信する
            contractManager.balanceSyncBridge().syncTransfer(
                fromAccountId,
                toAccountId,
                zoneId,
                amount,
                TIMEOUT_HEIGHT,
                traceId
            );
        }

        if (miscValue1 != EMPTY_BYTES32 || bytes(miscValue2).length != 0) {
            contractManager.transferProxy().customTransfer(
                sendAccountId,
                fromAccountId,
                toAccountId,
                amount,
                miscValue1,
                miscValue2,
                memo,
                traceId
            );
        } else {
            TransferData memory data;
            {
                data.transferType = Constant._TRANSFER;
                data.bizZoneId = 0;
                data.sendAccountId = sendAccountId;
                data.fromAccountId = fromAccountId;
                data.toAccountId = toAccountId;
                data.amount = amount;
                data.memo = memo;
            }
            emitData = transfer(contractManager, data);
        }
    }

    /**
     * @dev 送金処理を実行する
     *
     * @param contractManager ContractManager参照
     * @param data 送金データ
     * @return 送金データ
     */
    function transfer(IContractManager contractManager, TransferData memory data)
        public
        returns (TransferData memory)
    {
        string memory err;
        {
            // sendAccountIdとfromAccountIdが異なる場合にAllowanceのチェックを行う。IBCから呼ばれる場合は通らない。
            if (data.sendAccountId != data.fromAccountId) {
                // Allowanceの設定額を確認する
                uint256 allowance;
                (allowance, , err) = contractManager.account().getAllowance(
                    data.fromAccountId,
                    data.sendAccountId
                );
                require(data.amount <= allowance, Error.UE4401_ALLOWANCE_NOT_ENOUGH);
                // Allowanceの減額を行う
                contractManager.account().calcAllowance(
                    data.fromAccountId,
                    data.sendAccountId,
                    data.amount
                );
            }
        }
        // Event伝播用のaccount名義情報を取得し、上書きする
        AccountDataWithoutZoneId memory accountData;

        // 取引種別がchargeの場合
        if (data.transferType == Constant._CHARGE) {
            (accountData, ) = contractManager.account().getAccount(data.fromAccountId);
            data.fromAccountName = accountData.accountName;

            // fromAccountIdからフィルタリング用のvalidatorIdを取得
            (data.fromValidatorId, ) = contractManager.account().getValidatorIdByAccountId(
                data.fromAccountId
            );
        }
        // 取引種別がdischargeの場合
        else if (data.transferType == Constant._DISCHARGE) {
            (accountData, ) = contractManager.account().getAccount(data.toAccountId);
            data.toAccountName = accountData.accountName;

            // toAccountIdからフィルタリング用のvalidatorIdを取得
            (data.toValidatorId, ) = contractManager.account().getValidatorIdByAccountId(
                data.toAccountId
            );
        }
        // その他の取引種別の場合
        else {
            (accountData, ) = contractManager.account().getAccount(data.fromAccountId);
            data.fromAccountName = accountData.accountName;
            (accountData, ) = contractManager.account().getAccount(data.toAccountId);
            data.toAccountName = accountData.accountName;

            // fromAccountIdからフィルタリング用のvalidatorIdを取得
            (data.fromValidatorId, ) = contractManager.account().getValidatorIdByAccountId(
                data.fromAccountId
            );
            (data.toValidatorId, ) = contractManager.account().getValidatorIdByAccountId(
                data.toAccountId
            );
        }

        {
            // 送金を行う
            (data.fromAccountBalance, data.toAccountBalance) = contractManager
                .account()
                .calcBalance(data.fromAccountId, data.toAccountId, data.amount);

            // 取引種別がcharge, dischargeであれば、それぞれBizZone残高を取得する
            if (data.transferType == Constant._CHARGE || data.transferType == Constant._DISCHARGE) {
                BusinessZoneAccountData memory bizData = contractManager
                    .businessZoneAccount()
                    .getBusinessZoneAccount(
                        data.bizZoneId,
                        data.transferType == Constant._CHARGE
                            ? data.fromAccountId
                            : data.toAccountId
                    );
                data.businessZoneBalance = bizData.balance;
            }

            (data.zoneId, , err) = contractManager.provider().getZone();
            require(bytes(err).length == 0, err);

            //Accountに紐づくvalidatorIdを取得(bcmonitoringのフィルタリング用)
            (data.fromValidatorId, ) = contractManager.account().getValidatorIdByAccountId(
                data.fromAccountId
            );

            (data.toValidatorId, ) = contractManager.account().getValidatorIdByAccountId(
                data.toAccountId
            );

            return data;
        }
    }
}
