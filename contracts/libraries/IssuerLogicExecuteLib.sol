// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import {IContractManager} from "../interfaces/IContractManager.sol";
import {IIssuerStorage} from "../interfaces/IIssuerStorage.sol";
import {IssuerData, IssuerAll} from "../interfaces/Struct.sol";

/**
 * @dev IssuerLogicExecuteLibライブラリ
 *      Issuerの実行関数を実装するヘルパーライブラリ
 */
library IssuerLogicExecuteLib {
    /** @dev Issuerロール計算用(calcRole()のprefix用文字列(Issuer権限)) */
    bytes32 public constant ROLE_PREFIX_ISSUER = keccak256("ISSUER_ROLE");

    /**
     * @dev Issuerの追加処理を実行する
     *
     * @param issuerStorage IssuerStorage参照
     * @param contractManager ContractManager参照
     * @param issuerId issuerId
     * @param bankCode 金融機関コード
     * @param name issuer名
     */
    function executeAddIssuer(
        IIssuerStorage issuerStorage,
        IContractManager contractManager,
        bytes32 issuerId,
        uint16 bankCode,
        string memory name
    ) external {
        issuerStorage.addIssuerId(issuerId);
        issuerStorage.setIssuerIdExistence(issuerId, true);

        bytes32 role = contractManager.accessCtrl().calcRole(ROLE_PREFIX_ISSUER, issuerId);
        IssuerData memory issuerData = IssuerData({
            role: role,
            name: name,
            bankCode: bankCode,
            accountIds: new bytes32[](0)
        });
        issuerStorage.setIssuerData(issuerId, issuerData);
    }

    /**
     * @dev issuerにaccountを紐付ける処理を実行する
     *
     * @param issuerStorage IssuerStorage参照
     * @param issuerId issuerId
     * @param accountId accountId
     */
    function executeAddAccountId(
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        bytes32 accountId
    ) external {
        issuerStorage.addAccountIdToIssuer(issuerId, accountId);
        issuerStorage.setAccountIdExistenceByIssuerId(issuerId, accountId, true);
    }

    /**
     * @dev issuer名の更新処理を実行する
     *
     * @param issuerStorage IssuerStorage参照
     * @param issuerId issuerId
     * @param name issuer名
     */
    function updateIssuerName(
        IIssuerStorage issuerStorage,
        bytes32 issuerId,
        string memory name
    ) external {
        issuerStorage.updateIssuerName(issuerId, name);
    }

    /**
     * @dev バックアップ用に全発行者データを設定する
     *
     * @param issuerStorage IssuerStorage参照
     * @param issuer 全発行者データ
     * @param deadline 署名の期限
     * @param signature Admin署名
     */
    function setIssuerAll(
        IIssuerStorage issuerStorage,
        IssuerAll memory issuer,
        uint256 deadline,
        bytes memory signature
    ) external {
        issuerStorage.setIssuerAll(issuer, deadline, signature);
    }
}
