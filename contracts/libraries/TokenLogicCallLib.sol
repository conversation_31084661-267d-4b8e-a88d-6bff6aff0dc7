// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/ITokenStorage.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

/**
 * @dev TokenLogicCallLibライブラリ
 *      Tokenのview/pure関数を実装するヘルパーライブラリ
 *      ブロックチェーンデータの変更は行わない
 */
library TokenLogicCallLib {
    /** @dev 外部署名の検証に使用する固定値 */
    bytes32 private constant STRING_APPROVE = "approve";
    /** @dev バリデーション用のステータス値(解約済) */
    bytes32 private constant STATUS_TERMINATED = "terminated";

    ///////////////////////////////////
    // Validation Functions
    ///////////////////////////////////
    /**
     * @dev アカウント解約状況の確認
     *
     * @param contractManager ContractManager参照
     * @param accountId アカウントID
     */
    function checkAccountTermination(IContractManager contractManager, bytes32 accountId)
        public
        view
    {
        bool terminated;
        string memory err;

        (terminated, err) = contractManager.account().isTerminated(accountId);
        require(bytes(err).length == 0, err);
        require(!terminated, Error.GE2007_ACCOUNT_TERMINATED);
    }

    /**
     * @dev Token取得時の検証処理
     *
     * @param contractManager ContractManager参照
     * @param sender 送信者アドレス
     */
    function getTokenIsValid(IContractManager contractManager, address sender) external view {
        require(sender == address(contractManager.provider()), Error.GA0005_NOT_PROVIDER_CONTRACT);
    }

    /**
     * @dev Token追加時の検証処理
     *
     * @param contractManager ContractManager参照
     * @param tokenStorage TokenStorage参照
     * @param tokenId トークンID
     * @param sender 送信者アドレス
     */
    function addTokenIsValid(
        IContractManager contractManager,
        ITokenStorage tokenStorage,
        bytes32 tokenId,
        address sender
    ) external view {
        require(sender == address(contractManager.provider()), Error.GA0005_NOT_PROVIDER_CONTRACT);
        require(tokenId != 0x00, Error.RV0008_TOKEN_INVALID_VAL);
        require(tokenStorage.getTokenId() == 0x00, Error.GE1012_TOKEN_ID_EXIST);
    }

    /**
     * @dev Token修正時の検証処理
     *
     * @param contractManager ContractManager参照
     * @param tokenStorage TokenStorage参照
     * @param tokenId トークンID
     * @param sender 送信者アドレス
     */
    function modTokenIsValid(
        IContractManager contractManager,
        ITokenStorage tokenStorage,
        bytes32 tokenId,
        address sender
    ) external view {
        require(sender == address(contractManager.provider()), Error.GA0019_NOT_VALIDATOR_CONTRACT);
        require(tokenId != 0x00, Error.RV0008_TOKEN_INVALID_VAL);
        require(tokenStorage.getTokenId() == tokenId, Error.GE2010_NOT_TOKEN_ID);
    }

    /**
     * @dev Token有効性設定時の検証処理
     *
     * @param contractManager ContractManager参照
     * @param tokenStorage TokenStorage参照
     * @param providerId プロバイダーID
     * @param tokenId トークンID
     * @param enabled 有効性フラグ
     * @param deadline 署名期限
     * @param signature 署名
     */
    function setTokenEnabledIsValid(
        IContractManager contractManager,
        ITokenStorage tokenStorage,
        bytes32 providerId,
        bytes32 tokenId,
        bool enabled,
        uint256 deadline,
        bytes memory signature
    ) external view {
        (bool success, string memory err) = contractManager.provider().hasProvider(providerId);
        require(success, Error.GE0101_PROV_ID_NOT_EXIST);
        bytes32 hash = keccak256(abi.encode(providerId, tokenId, enabled, deadline));
        //権限チェック
        bool has;
        (has, err) = contractManager.provider().checkRole(providerId, hash, deadline, signature);
        require(bytes(err).length == 0, err);
        require(has, Error.GA0004_PROV_NOT_ROLE);
        require(tokenStorage.getTokenId() == tokenId, Error.GE0106_TOKEN_ID_NOT_EXIST);
    }

    /**
     * @dev Validator存在確認
     *
     * @param contractManager ContractManager参照
     * @param validatorId バリデーターID
     */
    function hasValidator(IContractManager contractManager, bytes32 validatorId) external view {
        (bool success, ) = contractManager.validator().hasValidator(validatorId);
        require(success, Error.GE0108_VALIDATOR_ID_NOT_EXIST);
    }

    /**
     * @dev Mint処理の検証
     *
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     */
    function mintIsValid(bytes32 issuerId, bytes32 accountId) external pure {
        require(issuerId != 0x00, Error.RV0016_INVALID_ORGANIZATION_ID);
        require(accountId != 0x00, Error.RV0017_INVALID_ACCOUNT_ID);
    }

    /**
     * @dev Burn処理の検証
     *
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     */
    function burnIsValid(bytes32 issuerId, bytes32 accountId) external pure {
        require(issuerId != 0x00, Error.RV0016_INVALID_ORGANIZATION_ID);
        require(accountId != 0x00, Error.RV0017_INVALID_ACCOUNT_ID);
    }

    /**
     * @dev 償却取り消し処理の検証
     *
     * @param contractManager ContractManager参照
     * @param tokenStorage TokenStorage参照
     * @param burnCancelData 償却取り消しデータ
     */
    function burnCancelIsValid(
        IContractManager contractManager,
        ITokenStorage tokenStorage,
        BurnCancelData memory burnCancelData
    ) external view {
        string memory err;
        bool success;
        require(
            burnCancelData.issuerId != 0x00 && burnCancelData.accountId != 0x00,
            Error.RV0008_TOKEN_INVALID_VAL // TODO: エラーメッセージが不適切なので修正する
        );
        // 解約確認を行う
        checkAccountTermination(contractManager, burnCancelData.accountId);
        //Issuer権限確認
        bytes32 hash = keccak256(
            abi.encode(
                burnCancelData.issuerId,
                burnCancelData.accountId,
                burnCancelData.amount,
                burnCancelData.blockTimestamp,
                burnCancelData.deadline
            )
        );
        (success, err) = contractManager.issuer().checkRole(
            burnCancelData.issuerId,
            hash,
            burnCancelData.deadline,
            burnCancelData.signature
        );
        require(bytes(err).length == 0, err);
        require(success, Error.GA0007_ISSUER_NOT_ROLE);
        //AccountID存在確認
        (success, err) = contractManager.issuer().hasAccount(
            burnCancelData.issuerId,
            burnCancelData.accountId
        );
        require(success, err);
        // zoneIdの取得
        (, , err) = contractManager.provider().getZone();
        require(bytes(err).length == 0, err);

        //TotalSupply増額
        require(
            tokenStorage.getTokenData(burnCancelData.tokenId).totalSupply + burnCancelData.amount >=
                tokenStorage.getTokenData(burnCancelData.tokenId).totalSupply,
            Error.SR0003_TOKEN_OVERFLOW
        );
    }

    ///////////////////////////////////
    // Token Information Functions
    ///////////////////////////////////

    /**
     * @dev Token情報を取得する
     *
     * @param tokenStorage TokenStorage参照
     * @param tokenId トークンID
     * @return name トークンIDに紐づくトークンの名
     * @return symbol トークンIDに紐づくトークンのsymbol
     * @return totalSupply トークンIDに紐づくトークンの総供給量
     * @return enabled トークンIDに紐づくture:有効,false:無効
     * @return err エラーメッセージ
     */
    function getToken(ITokenStorage tokenStorage, bytes32 tokenId)
        public
        view
        returns (
            bytes32 name,
            bytes32 symbol,
            uint256 totalSupply,
            bool enabled,
            string memory err
        )
    {
        TokenData memory tokenData = tokenStorage.getTokenData(tokenId);
        return (tokenData.name, tokenData.symbol, tokenData.totalSupply, tokenData.enabled, "");
    }

    /**
     * @dev ビジネスゾーンの全残高情報を取得する
     *
     * @param contractManager ContractManager参照
     * @param accountId accountId
     * @return zoneIds ビジネスゾーンID
     * @return zoneNames ゾーン名
     * @return balances 残高
     * @return accountNames アカウント名
     * @return accountStatus アカウントステータス
     * @return totalBalance 合計残高
     */
    function getBalanceList(IContractManager contractManager, bytes32 accountId)
        external
        view
        returns (
            uint16[] memory zoneIds,
            string[] memory zoneNames,
            uint256[] memory balances,
            string[] memory accountNames,
            bytes32[] memory accountStatus,
            uint256 totalBalance,
            string memory err
        )
    {
        bool success;
        (success, err) = contractManager.account().hasAccount(accountId);
        if (!success) {
            return (zoneIds, zoneNames, balances, accountNames, accountStatus, 0, err);
        }

        ZoneData[] memory zones = contractManager.account().getZoneByAccountId(accountId);

        zoneIds = new uint16[](zones.length);
        zoneNames = new string[](zones.length);
        balances = new uint256[](zones.length);
        accountNames = new string[](zones.length);
        accountStatus = new bytes32[](zones.length);

        for (uint256 lp = 0; lp < zones.length; lp++) {
            uint16 zoneId = zones[lp].zoneId;

            BusinessZoneAccountData memory data = contractManager
                .businessZoneAccount()
                .getBusinessZoneAccount(zoneId, accountId);
            if (data.accountStatus != STATUS_TERMINATED) {
                zoneIds[lp] = zoneId;
                zoneNames[lp] = contractManager.provider().getZoneName(zoneId);
                balances[lp] = data.balance;
                accountNames[lp] = data.accountName;
                accountStatus[lp] = data.accountStatus;
                totalBalance += data.balance;
            }
        }

        return (zoneIds, zoneNames, balances, accountNames, accountStatus, totalBalance, "");
    }

    ///////////////////////////////////
    // Approval Validation Functions
    ///////////////////////////////////

    /**
     * @dev 送金許可の事前チェック
     *
     * @param contractManager ContractManager参照
     * @param validatorId バリデーターID
     * @param ownerId オーナーID
     * @param spenderId スペンダーID
     * @param amount 許可額
     * @param accountSignature アカウント署名
     * @param info 署名情報
     * @param deadline 期限
     * @param signature バリデーター署名
     * @return success 成功フラグ
     * @return err エラーメッセージ
     */
    function checkApprove(
        IContractManager contractManager,
        bytes32 validatorId,
        bytes32 ownerId,
        bytes32 spenderId,
        uint256 amount,
        bytes memory accountSignature,
        bytes memory info,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool success, string memory err) {
        (success, err) = contractManager.validator().hasValidator(validatorId);
        if (!success) {
            return (success, err);
        }

        // 最大限度額を超えている場合はエラー
        if (amount > Constant._MAX_ALLOWANCE_VALUE) {
            return (false, Error.ACCOUNT_EXCEED_APPROVAL_LIMIT);
        }

        // AccountとValidatorId紐付けられていない場合はエラー
        if (!contractManager.validator().hasValidatorByAccount(validatorId, ownerId)) {
            return (false, Error.GE0108_VALIDATOR_ID_NOT_EXIST);
        }

        //権限チェック
        bool has;
        (has, err) = contractManager.validator().hasValidatorRole(
            validatorId,
            keccak256(abi.encode(validatorId, ownerId, deadline)),
            deadline,
            signature
        );
        if (bytes(err).length != 0) {
            return (false, err);
        }
        if (!has) {
            return (false, Error.GA0018_VALIDATOR_NOT_ROLE);
        }

        // Accountの署名を検証する
        (success, err) = contractManager.accessCtrl().checkSigAccount(
            ownerId,
            keccak256(abi.encode(ownerId, spenderId, amount, STRING_APPROVE)),
            accountSignature,
            info
        );
        if (success == false) {
            return (success, err);
        }

        // Ownerの有効性確認
        {
            (success, err) = contractManager.account().isActivated(ownerId);
            if (success == false) {
                return (success, err);
            }
        }

        // Spenderの有効性確認
        {
            (success, err) = contractManager.account().isActivated(spenderId);
            if (success == false) {
                return (success, err);
            }
        }

        return (true, "");
    }

    ///////////////////////////////////
    // Token Data Functions
    ///////////////////////////////////

    /**
     * @dev Token存在確認
     *
     * @param tokenStorage TokenStorage参照
     * @param tokenId 確認するトークンID
     * @param chkTokenId チェック対象のトークンID
     * @param chkEnabled 有効性確認フラグ
     * @return success 存在フラグ
     * @return err エラーメッセージ
     */
    function hasToken(
        ITokenStorage tokenStorage,
        bytes32 tokenId,
        bytes32 chkTokenId,
        bool chkEnabled
    ) public view returns (bool success, string memory err) {
        if (chkTokenId == 0x00) {
            return (false, Error.RV0008_TOKEN_INVALID_VAL);
        }
        if (tokenId == chkTokenId) {
            if (chkEnabled) {
                return
                    tokenStorage.getTokenData(chkTokenId).enabled
                        ? (true, "")
                        : (false, Error.GE2008_TOKEN_DISABLED);
            } else {
                return (true, "");
            }
        }
        return (false, Error.GE0106_TOKEN_ID_NOT_EXIST);
    }

    /**
     * @dev Token情報の取得（Provider権限チェック付き）
     *
     * @param tokenStorage TokenStorage参照
     * @return tokenId tokenId
     * @return name token名
     * @return symbol symbol
     * @return totalSupply tokenの総供給量
     * @return enabled ture:有効,false:無効
     * @return err エラーメッセージ
     */
    function getToken(ITokenStorage tokenStorage)
        external
        view
        returns (
            bytes32 tokenId,
            bytes32 name,
            bytes32 symbol,
            uint256 totalSupply,
            bool enabled,
            string memory err
        )
    {
        tokenId = tokenStorage.getTokenId();
        bool success;
        (success, err) = hasToken(tokenStorage, tokenId, tokenId, true);
        if (!success) {
            // TODO: Refactor時に_EMPTY_xxxに修正
            return (0x00, 0x00, 0x00, 0, false, Error.GE0107_TOKEN_NOT_EXIST);
        }
        (name, symbol, totalSupply, enabled, err) = getToken(tokenStorage, tokenId);
        return (tokenId, name, symbol, totalSupply, enabled, err);
    }

    /**
     * @dev Token全情報取得
     *      既に登録されているTokenの全情報を取得する
     *
     * @param tokenStorage TokenStorage参照
     * @return token 全Tokenの情報
     */
    function getTokenAll(ITokenStorage tokenStorage) external view returns (TokenAll memory token) {
        return tokenStorage.getTokenAll();
    }

    /**
     * @dev TokenId取得
     *
     * @param tokenStorage TokenStorage参照
     * @return tokenId TokenId
     */
    function getTokenId(ITokenStorage tokenStorage) external view returns (bytes32 tokenId) {
        return tokenStorage.getTokenId();
    }

    /**
     * @dev 許可額情報を取得する
     *
     * @param contractManager ContractManager参照
     * @param validatorId バリデーターID
     * @param ownerId オーナーID
     * @param spenderId スペンダーID
     * @return allowance 許可額
     * @return approvedAt 許可日時
     * @return err エラーメッセージ
     */
    function getAllowance(
        IContractManager contractManager,
        bytes32 validatorId,
        bytes32 ownerId,
        bytes32 spenderId
    )
        external
        view
        returns (
            uint256 allowance,
            uint256 approvedAt,
            string memory err
        )
    {
        (bool success, string memory errHas) = contractManager.validator().hasAccount(
            validatorId,
            ownerId
        );
        if (!success) {
            return (0, 0, errHas);
        }
        return contractManager.account().getAllowance(ownerId, spenderId);
    }
}
