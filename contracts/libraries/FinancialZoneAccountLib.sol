// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

library FinancialZoneAccountLib {
    /**
     * @dev アカウントの限度額を取得する
     *
     * @param accountLimitMapping 取得対象となるアカウント限度額設定のマッピングデータ
     * @param key マッピングのキーとなるアカウントID
     * @return accountLimitData
     */
    function getAccountLimitData(
        mapping(bytes32 => FinancialZoneAccountData) storage accountLimitMapping,
        bytes32 key
    ) external view returns (FinancialZoneAccountData memory accountLimitData) {
        return accountLimitMapping[key];
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev アカウント限度額登録
     *
     * @param accountLimitDataMapping 登録先のアカウント限度額のマッピングデータ
     * @param key マッピングのキーとなるアカウントID
     * @param limitValues アカウントの限度額値
     */
    function addAccountLimitData(
        mapping(bytes32 => FinancialZoneAccountData) storage accountLimitDataMapping,
        bytes32 key,
        AccountLimitValues memory limitValues
    ) external {
        {
            require(
                limitValues.mint <= Constant._MAX_LIMIT_VALUE,
                Error.UE4001_EXCEEDED_MINT_LIMIT
            );
            require(
                limitValues.burn <= Constant._MAX_LIMIT_VALUE,
                Error.UE4002_EXCEEDED_BURN_LIMIT
            );
            require(
                limitValues.charge <= Constant._MAX_LIMIT_VALUE,
                Error.UE4004_EXCEEDED_CHARGE_LIMIT
            );
            require(
                limitValues.discharge <= Constant._MAX_LIMIT_VALUE,
                Error.EXCEEDED_DISCHARGE_LIMIT
            );
            require(
                limitValues.transfer <= Constant._MAX_LIMIT_VALUE,
                Error.UE4003_EXCEEDED_TRANSFER_LIMIT
            );
            require(
                limitValues.cumulative.total <= Constant._MAX_DAILY_LIMIT_VALUE,
                Error.UE4101_EXCEEDED_DAILY_LIMIT
            );
            require(
                limitValues.cumulative.mint <= Constant._MAX_DAILY_LIMIT_VALUE,
                Error.EXCEEDED_DAILY_MINT_LIMIT
            );
            require(
                limitValues.cumulative.burn <= Constant._MAX_DAILY_LIMIT_VALUE,
                Error.EXCEEDED_DAILY_BURN_LIMIT
            );
            require(
                limitValues.cumulative.charge <= Constant._MAX_DAILY_LIMIT_VALUE,
                Error.EXCEEDED_DAILY_CHARGE_LIMIT
            );
            require(
                limitValues.cumulative.discharge <= Constant._MAX_DAILY_LIMIT_VALUE,
                Error.EXCEEDED_DAILY_DISCHARGE_LIMIT
            );
            require(
                limitValues.cumulative.transfer <= Constant._MAX_DAILY_LIMIT_VALUE,
                Error.EXCEEDED_DAILY_TRANSFER_LIMIT
            );
        }
        accountLimitDataMapping[key].mintLimit = limitValues.mint;
        accountLimitDataMapping[key].burnLimit = limitValues.burn;
        accountLimitDataMapping[key].chargeLimit = limitValues.charge;
        accountLimitDataMapping[key].dischargeLimit = limitValues.discharge;
        accountLimitDataMapping[key].transferLimit = limitValues.transfer;
        accountLimitDataMapping[key].cumulativeLimit = limitValues.cumulative.total;
        accountLimitDataMapping[key].cumulativeTransactionLimits.cumulativeMintLimit = limitValues
            .cumulative
            .mint;
        accountLimitDataMapping[key].cumulativeTransactionLimits.cumulativeBurnLimit = limitValues
            .cumulative
            .burn;
        accountLimitDataMapping[key].cumulativeTransactionLimits.cumulativeChargeLimit = limitValues
            .cumulative
            .charge;
        accountLimitDataMapping[key]
            .cumulativeTransactionLimits
            .cumulativeDischargeLimit = limitValues.cumulative.discharge;
        accountLimitDataMapping[key]
            .cumulativeTransactionLimits
            .cumulativeTransferLimit = limitValues.cumulative.transfer;
    }

    /**
     * @dev アカウント限度額登録
     *
     * @param accountLimitDataMapping 登録先のアカウント限度額のマッピングデータ
     * @param key マッピングのキーとなるアカウントID
     * @param limitUpdates アカウント限度額の更新フラグ
     * @param limitValues アカウントの限度額値
     */
    function modAccountLimitData(
        mapping(bytes32 => FinancialZoneAccountData) storage accountLimitDataMapping,
        bytes32 key,
        AccountLimitUpdates memory limitUpdates,
        AccountLimitValues memory limitValues
    ) external returns (AccountLimitValues memory) {
        // 各フラグに応じて限度額を設定
        // 発行限度額の更新
        if (limitUpdates.mint) {
            require(
                limitValues.mint <= Constant._MAX_LIMIT_VALUE,
                Error.UE4001_EXCEEDED_MINT_LIMIT
            );
            accountLimitDataMapping[key].mintLimit = limitValues.mint;
        }

        // 償却限度額の更新
        if (limitUpdates.burn) {
            require(
                limitValues.burn <= Constant._MAX_LIMIT_VALUE,
                Error.UE4002_EXCEEDED_BURN_LIMIT
            );
            accountLimitDataMapping[key].burnLimit = limitValues.burn;
        }

        // チャージ限度額の更新
        if (limitUpdates.charge) {
            require(
                limitValues.charge <= Constant._MAX_LIMIT_VALUE,
                Error.UE4004_EXCEEDED_CHARGE_LIMIT
            );
            accountLimitDataMapping[key].chargeLimit = limitValues.charge;
        }

        //  返還限度額の更新
        if (limitUpdates.discharge) {
            require(
                limitValues.discharge <= Constant._MAX_LIMIT_VALUE,
                Error.EXCEEDED_DISCHARGE_LIMIT
            );
            accountLimitDataMapping[key].dischargeLimit = limitValues.discharge;
        }

        //  送金限度額の更新
        if (limitUpdates.transfer) {
            require(
                limitValues.transfer <= Constant._MAX_LIMIT_VALUE,
                Error.UE4003_EXCEEDED_TRANSFER_LIMIT
            );
            accountLimitDataMapping[key].transferLimit = limitValues.transfer;
        }

        // 一日の全取引の合計累積限度額の更新
        if (limitUpdates.cumulative.total) {
            require(
                limitValues.cumulative.total <= Constant._MAX_DAILY_LIMIT_VALUE,
                Error.UE4101_EXCEEDED_DAILY_LIMIT
            );
            accountLimitDataMapping[key].cumulativeLimit = limitValues.cumulative.total;
        }

        // 発行累積限度額の更新
        if (limitUpdates.cumulative.mint) {
            require(
                limitValues.cumulative.mint <= Constant._MAX_DAILY_LIMIT_VALUE,
                Error.EXCEEDED_DAILY_MINT_LIMIT
            );
            accountLimitDataMapping[key]
                .cumulativeTransactionLimits
                .cumulativeMintLimit = limitValues.cumulative.mint;
        }
        //償却累積限度額の更新
        if (limitUpdates.cumulative.burn) {
            require(
                limitValues.cumulative.burn <= Constant._MAX_DAILY_LIMIT_VALUE,
                Error.EXCEEDED_DAILY_BURN_LIMIT
            );
            accountLimitDataMapping[key]
                .cumulativeTransactionLimits
                .cumulativeBurnLimit = limitValues.cumulative.burn;
        }

        // チャージ累積限度額の更新
        if (limitUpdates.cumulative.charge) {
            require(
                limitValues.cumulative.charge <= Constant._MAX_DAILY_LIMIT_VALUE,
                Error.EXCEEDED_DAILY_CHARGE_LIMIT
            );
            accountLimitDataMapping[key]
                .cumulativeTransactionLimits
                .cumulativeChargeLimit = limitValues.cumulative.charge;
        }

        // 返還累積限度額の更新
        if (limitUpdates.cumulative.discharge) {
            require(
                limitValues.cumulative.discharge <= Constant._MAX_DAILY_LIMIT_VALUE,
                Error.EXCEEDED_DAILY_DISCHARGE_LIMIT
            );
            accountLimitDataMapping[key]
                .cumulativeTransactionLimits
                .cumulativeDischargeLimit = limitValues.cumulative.discharge;
        }

        // 移転累積限度額の更新
        if (limitUpdates.cumulative.transfer) {
            require(
                limitValues.cumulative.transfer <= Constant._MAX_DAILY_LIMIT_VALUE,
                Error.EXCEEDED_DAILY_TRANSFER_LIMIT
            );
            accountLimitDataMapping[key]
                .cumulativeTransactionLimits
                .cumulativeTransferLimit = limitValues.cumulative.transfer;
        }

        // Event用に設定後のAccountの限度額Listを作成
        limitValues.mint = accountLimitDataMapping[key].mintLimit;
        limitValues.burn = accountLimitDataMapping[key].burnLimit;
        limitValues.charge = accountLimitDataMapping[key].chargeLimit;
        limitValues.discharge = accountLimitDataMapping[key].dischargeLimit;
        limitValues.transfer = accountLimitDataMapping[key].transferLimit;
        limitValues.cumulative.total = accountLimitDataMapping[key].cumulativeLimit;
        limitValues.cumulative.mint = accountLimitDataMapping[key]
            .cumulativeTransactionLimits
            .cumulativeMintLimit;
        limitValues.cumulative.burn = accountLimitDataMapping[key]
            .cumulativeTransactionLimits
            .cumulativeBurnLimit;
        limitValues.cumulative.charge = accountLimitDataMapping[key]
            .cumulativeTransactionLimits
            .cumulativeChargeLimit;
        limitValues.cumulative.discharge = accountLimitDataMapping[key]
            .cumulativeTransactionLimits
            .cumulativeDischargeLimit;
        limitValues.cumulative.transfer = accountLimitDataMapping[key]
            .cumulativeTransactionLimits
            .cumulativeTransferLimit;

        return limitValues;
    }

    /**
     * @dev アカウント限度額初期化
     *
     * @param accountLimitDataMapping 登録先のアカウント限度額のマッピングデータ
     * @param key マッピングのキーとなるアカウントID
     * @param jstDay 現在の日付
     */
    function resetCumulative(
        mapping(bytes32 => FinancialZoneAccountData) storage accountLimitDataMapping,
        bytes32 key,
        uint256 jstDay
    ) external {
        accountLimitDataMapping[key].cumulativeAmount = Constant._RESET_CUMULATIVE_AMOUNT;
        accountLimitDataMapping[key].cumulativeTransactionLimits.cumulativeMintAmount = Constant
            ._RESET_CUMULATIVE_AMOUNT;
        accountLimitDataMapping[key].cumulativeTransactionLimits.cumulativeBurnAmount = Constant
            ._RESET_CUMULATIVE_AMOUNT;
        accountLimitDataMapping[key].cumulativeTransactionLimits.cumulativeChargeAmount = Constant
            ._RESET_CUMULATIVE_AMOUNT;
        accountLimitDataMapping[key]
            .cumulativeTransactionLimits
            .cumulativeDischargeAmount = Constant._RESET_CUMULATIVE_AMOUNT;
        accountLimitDataMapping[key].cumulativeTransactionLimits.cumulativeTransferAmount = Constant
            ._RESET_CUMULATIVE_AMOUNT;
        accountLimitDataMapping[key].cumulativeDate = jstDay;
    }

    /**
     * @dev 累積限度額の加算を行う TODO:減算処理と統合する？
     *
     * @param accountLimitDataMapping 登録先のアカウント限度額マッピングデータ
     * @param key マッピングのキーとなるアカウントID
     * @param amount 金額
     * @param currentDay 現在の日付
     * @return cumulativeDate
     * @return cumulativeAmount
     */
    function addAmount(
        mapping(bytes32 => FinancialZoneAccountData) storage accountLimitDataMapping,
        bytes32 key,
        uint256 amount,
        uint256 currentDay
    ) external returns (uint256 cumulativeDate, uint256 cumulativeAmount) {
        if (accountLimitDataMapping[key].cumulativeDate == currentDay) {
            accountLimitDataMapping[key].cumulativeAmount += amount;
        } else {
            accountLimitDataMapping[key].cumulativeAmount = amount;
            accountLimitDataMapping[key].cumulativeDate = currentDay;
        }

        return (
            accountLimitDataMapping[key].cumulativeDate,
            accountLimitDataMapping[key].cumulativeAmount
        );
    }

    /**
     * @dev 累積限度額の減算を行う
     *
     * @param accountLimitDataMapping 登録先のアカウント限度額マッピングデータ
     * @param key マッピングのキーとなるアカウントID
     * @param amount 金額
     * @return cumulativeDate
     * @return cumulativeAmount
     */

    function subtractAmount(
        mapping(bytes32 => FinancialZoneAccountData) storage accountLimitDataMapping,
        bytes32 key,
        uint256 amount
    ) external returns (uint256 cumulativeDate, uint256 cumulativeAmount) {
        accountLimitDataMapping[key].cumulativeAmount -= amount;
        return (
            accountLimitDataMapping[key].cumulativeDate,
            accountLimitDataMapping[key].cumulativeAmount
        );
    }

    /**
     * @dev 操作額を各種累積限度額に反映させる
     *
     * @param accountLimitDataMapping 登録先のアカウント限度額マッピングデータ
     * @param key アカウントID
     * @param amount 金額
     * @param currentDay 現在の日付
     * @param operationType 取引種別（Constant._SYNC_MINT, _SYNC_BURN, _SYNC_CHARGE, _SYNC_DISCHARGE, _SYNC_TRANSFER）
     */
    function syncLimitAmount(
        mapping(bytes32 => FinancialZoneAccountData) storage accountLimitDataMapping,
        bytes32 key,
        uint256 amount,
        uint256 currentDay,
        bytes32 operationType
    ) external {
        FinancialZoneAccountData storage data = accountLimitDataMapping[key];
        CumulativeTransactionLimits storage limits = data.cumulativeTransactionLimits;

        // 累積日付が更新されるべき場合（cumulativeDate < currentDay）に累積額をリセット
        bool isNewCumulativeDay = data.cumulativeDate < currentDay;

        if (isNewCumulativeDay) {
            // 累積日付が古い場合は累積額をリセットして新しい値を設定
            data.cumulativeAmount = amount;

            // 全ての累積額をリセット
            limits.cumulativeMintAmount = Constant._RESET_CUMULATIVE_AMOUNT;
            limits.cumulativeBurnAmount = Constant._RESET_CUMULATIVE_AMOUNT;
            limits.cumulativeChargeAmount = Constant._RESET_CUMULATIVE_AMOUNT;
            limits.cumulativeDischargeAmount = Constant._RESET_CUMULATIVE_AMOUNT;
            limits.cumulativeTransferAmount = Constant._RESET_CUMULATIVE_AMOUNT;

            data.cumulativeDate = currentDay;
        } else {
            // 同じ日の場合は累積額に加算
            data.cumulativeAmount += amount;
        }

        // 取引種別に応じた累積額を更新
        if (operationType == Constant._SYNC_MINT) {
            limits.cumulativeMintAmount += amount;
        } else if (operationType == Constant._SYNC_BURN) {
            limits.cumulativeBurnAmount += amount;
        } else if (operationType == Constant._SYNC_CHARGE) {
            limits.cumulativeChargeAmount += amount;
        } else if (operationType == Constant._SYNC_DISCHARGE) {
            limits.cumulativeDischargeAmount += amount;
        } else if (operationType == Constant._SYNC_TRANSFER) {
            limits.cumulativeTransferAmount += amount;
        }
    }
}
