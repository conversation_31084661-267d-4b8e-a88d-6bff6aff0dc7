/* eslint-disable import/no-extraneous-dependencies */
import { GetPublicKeyCommand, KMSClient, KMSClientConfig, SignCommand } from '@aws-sdk/client-kms'
import { ECDSASigValue } from '@peculiar/asn1-ecc'
import { AsnConvert } from '@peculiar/asn1-schema'
import { SubjectPublicKeyInfo } from '@peculiar/asn1-x509'
import { AwsCredentialIdentity, AwsCredentialIdentityProvider } from '@smithy/types'
import {
  AbiCoder,
  AbstractSigner,
  assert,
  assertArgument,
  BytesLike,
  dataLength,
  getAddress,
  getBytes,
  hashMessage,
  keccak256,
  NonceManager,
  Provider,
  recoverAddress as recoverAddressFn,
  resolveAddress,
  resolveProperties,
  N as secp256k1N,
  Signature,
  toBeHex,
  toBigInt,
  Transaction,
  TransactionLike,
  TransactionRequest,
  TypedDataDomain,
  TypedDataEncoder,
  TypedDataField,
  ParamType,
} from 'ethers'

export type EthersAwsKmsSignerConfig = {
  credentials: AwsCredentialIdentityProvider | AwsCredentialIdentity
  region: string
  keyId: string
  endpoint?: string
}

class AwsKmsBaseSigner<P extends null | Provider = null | Provider> extends AbstractSigner {
  protected config: EthersAwsKmsSignerConfig
  protected client: KMSClient

  address!: string

  constructor(config: EthersAwsKmsSignerConfig, provider?: P) {
    super(provider)
    if (!process.env.PROJECT_ENV) {
      this.config = config
      this.client = this._createKMSClient(
        config.region,
        config.credentials,
        config.endpoint, // endpointを渡す
      )
    } else {
      this.config = config
      const codebuildConfig = { region: this.config.region }
      this.client = new KMSClient(codebuildConfig)
    }
  }

  connect(provider: Provider | null): AwsKmsBaseSigner {
    return new AwsKmsBaseSigner(this.config, provider)
  }

  async getAddress(): Promise<string> {
    if (!this.address) {
      const command = new GetPublicKeyCommand({ KeyId: this.config.keyId })
      const response = await this.client.send(command)

      const publicKeyHex = response.PublicKey
      if (!publicKeyHex) {
        throw new Error(`Could not get Public Key from KMS.`)
      }

      const ecPublicKey = AsnConvert.parse(Buffer.from(publicKeyHex), SubjectPublicKeyInfo).subjectPublicKey

      this.address = getAddress(
        `0x${keccak256(new Uint8Array(ecPublicKey.slice(1, ecPublicKey.byteLength))).slice(-40)}`,
      )
    }

    return this.address
  }

  async signTransaction(tx: TransactionRequest): Promise<string> {
    const { to, from } = await resolveProperties({
      to: tx.to ? resolveAddress(tx.to, this.provider) : undefined,
      from: tx.from ? resolveAddress(tx.from, this.provider) : undefined,
    })

    if (to != null) {
      tx.to = to
    }
    if (from != null) {
      tx.from = from
    }

    const address = await this.getAddress()

    if (tx.from != null) {
      assertArgument(getAddress(tx.from as string) === address, 'transaction from address mismatch', 'tx.from', tx.from)
      delete tx.from
    }

    const btx = Transaction.from(tx as TransactionLike<string>)
    btx.signature = await this._sign(btx.unsignedHash)

    return btx.serialized
  }

  async signMessage(message: string | Uint8Array): Promise<string> {
    const signature = await this._sign(hashMessage(message))
    return signature.serialized
  }

  async signTypedData(
    domain: TypedDataDomain,
    types: Record<string, TypedDataField[]>,
    value: Record<string, any>,
  ): Promise<string> {
    const populated = await TypedDataEncoder.resolveNames(domain, types, value, async (name: string) => {
      assert(this.provider != null, 'cannot resolve ENS names without a provider', 'UNSUPPORTED_OPERATION', {
        operation: 'resolveName',
        info: { name },
      })

      const address = await this.provider.resolveName(name)
      assert(address != null, 'unconfigured ENS name', 'UNCONFIGURED_NAME', {
        value: name,
      })

      return address
    })

    const signature = await this._sign(TypedDataEncoder.hash(populated.domain, types, populated.value))

    return signature.serialized
  }

  async sign(types: (string | ParamType)[], data: any[]): Promise<Uint8Array> {
    const encodeData = AbiCoder.defaultAbiCoder().encode(types, data)
    const hash = keccak256(encodeData)
    const arrayifiedHash = getBytes(hash)
    const rawSignature = await this.signMessage(arrayifiedHash)

    return getBytes(rawSignature)
  }

  protected _createKMSClient(
    region: string,
    credentials: AwsCredentialIdentityProvider | AwsCredentialIdentity,
    endpoint?: string,
  ) {
    const config: KMSClientConfig = { region, credentials }
    if (endpoint) {
      config.endpoint = endpoint
    }
    return new KMSClient(config)
  }

  protected async _sign(digest: BytesLike): Promise<Signature> {
    assertArgument(dataLength(digest) === 32, 'invalid digest length', 'digest', digest)

    const command = new SignCommand({
      KeyId: this.config.keyId,
      Message: getBytes(digest),
      MessageType: 'DIGEST',
      SigningAlgorithm: 'ECDSA_SHA_256',
    })

    const response = await this.client.send(command)
    const signatureHex = response.Signature

    if (!signatureHex) {
      throw new Error('Could not fetch Signature from KMS.')
    }

    const signature = AsnConvert.parse(Buffer.from(signatureHex), ECDSASigValue)

    let s = toBigInt(new Uint8Array(signature.s))
    s = s > secp256k1N / BigInt(2) ? secp256k1N - s : s

    const rHex = toBeHex(toBigInt(new Uint8Array(signature.r)), 32)
    const sHex = toBeHex(s, 32)

    const recoverAddress = recoverAddressFn(digest, {
      r: rHex,
      s: sHex,
      v: 0x1b,
    })

    const address = await this.getAddress()

    return Signature.from({
      r: rHex,
      s: sHex,
      v: recoverAddress.toLowerCase() !== address.toLowerCase() ? 0x1c : 0x1b,
    })
  }
}

export class AwsKmsSigner<P extends null | Provider = null | Provider> extends AbstractSigner {
  private baseSigner: AwsKmsBaseSigner<P>
  private nonceManager: NonceManager
  private config: EthersAwsKmsSignerConfig

  constructor(config: EthersAwsKmsSignerConfig, provider?: P) {
    super(provider)
    this.config = config
    this.baseSigner = new AwsKmsBaseSigner(config, provider)
    this.nonceManager = new NonceManager(this.baseSigner)
  }

  connect(provider: Provider | null): AwsKmsSigner {
    return new AwsKmsSigner(this.config, provider)
  }

  async getAddress(): Promise<string> {
    return this.baseSigner.getAddress()
  }

  /*
   *@notice 通常のsignTransactionを利用するとNonce Errorが発生するため、NonceManagerを利用する
   *@dev: Add populating transaction nonce before signing
   */
  async signTransaction(tx: TransactionRequest): Promise<string> {
    await this.nonceManager.populateTransaction(tx)
    return this.nonceManager.signTransaction(tx)
  }

  async signMessage(message: string | Uint8Array): Promise<string> {
    return this.baseSigner.signMessage(message)
  }

  async signTypedData(
    domain: TypedDataDomain,
    types: Record<string, TypedDataField[]>,
    value: Record<string, any>,
  ): Promise<string> {
    return this.baseSigner.signTypedData(domain, types, value)
  }

  async sign(types: (string | ParamType)[], data: any[]): Promise<Uint8Array> {
    return this.baseSigner.sign(types, data)
  }
}
