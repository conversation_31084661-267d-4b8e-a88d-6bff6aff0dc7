/**
 * Hardhat Deployment Script(BizZone用)
 * 1. ネットワーク: 'local'と'main'を'hardhat.config.ts'で設定
 * 2. ウォレット: 'local'の場合、'admin'と'deployer'はデフォルトで設定されるプライベートキーを利用する
 * 3. デプロイライブラリ: 'hardhat-deploy'を利用し、Proxyとして'OpenZeppelinTransparentProxy'を設定
 */
import { getAddressFromABI } from '@deploy/common/abiTools'
import { showDeployNetwork, showDeploySuccessWithMsg } from '@deploy/common/consoles'
import { deployContractWithSaveABI } from '@deploy/common/deployContractWithSaveABI'
import { handleTransaction } from '@deploy/common/handleTransaction'
import { kmsSignerProvider } from '@deploy/common/kmsSignerProvider'
import { printSection } from '@tasks/common/tools'
import { ethers, network } from 'hardhat'
import { DeployFunction } from 'hardhat-deploy/types'

const func: DeployFunction = async () => {
  // hardhat-deployのdeploy()関数を利用してdeployを行う
  // deploy()の引数には送信者アドレスを設定するfrom, Link対象コントラクトを設定するlink, proxyコントラクトの実行を指定できるproxy, 詳細なlogを表示させるlogが存在する
  // Deployの順番は前後順番があるので注意する：(1)Library系(Errorなど) (2)ContractManager (3)Others
  // Deploy RenewableEnergyTokenLib
  // RenewableEnergyTokenはBizZone専用のカスタムコントラクト扱いであるため、main-bizネットワークの場合のみデプロイ対象とする
  // TODO: デプロイ時の運用方針の確定
  showDeployNetwork({ title: 'RenewableEnergyToken' })

  const contractMap = new Map()

  const kmsSigner = kmsSignerProvider()
  const deployerAddress = await kmsSigner.getAddress()

  // Initialize nonce management
  let currentNonceDefaultSinger = await ethers.provider.getTransactionCount(
    (await ethers.provider.getSigner(0)).getAddress(),
  )
  console.log(`Current nonce for default singer: ${currentNonceDefaultSinger}`)

  // RenewableEnergyTokenで利用するデプロイ済みコントラクトを取得
  const contractManagerAddress = await getAddressFromABI({ networkName: network.name, contractName: 'ContractManager' })
  const tokenAddress = await getAddressFromABI({ networkName: network.name, contractName: 'TokenLogic' })
  const errorAddress = await getAddressFromABI({ networkName: network.name, contractName: 'Error' })
  const transferProxyAddress = await getAddressFromABI({ networkName: network.name, contractName: 'TransferProxy' })

  const deployedContracts = `
    -------------------------------------------------------

    ContractManager= ${contractManagerAddress}
    Token=${tokenAddress}
    Error= ${errorAddress}
    TransferProxy= ${transferProxyAddress}

    ADMIN= ${deployerAddress}
    -------------------------------------------------------
    `
  console.log('All deployed contracts retrieved successfully', deployedContracts)

  // Deploy RenewableEnergyTokenLib
  const RenewableEnergyTokenLib = await deployContractWithSaveABI({
    contractName: 'RenewableEnergyTokenLib',
    contractMap,
    overrides: { nonce: currentNonceDefaultSinger++ },
  })

  // Deploy RenewableEnergyToken
  const renewableEnergyToken = await deployContractWithSaveABI({
    contractName: 'RenewableEnergyToken',
    options: {
      libraries: {
        RenewableEnergyTokenLib,
      },
    },
    initialize: {
      args: [contractManagerAddress, tokenAddress],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonceDefaultSinger++ },
    initializeOverrides: { nonce: currentNonceDefaultSinger++ },
  })

  // Deploy情報をFormatしてPrintする
  // Contract set to manageContract情報をFormatしてPrintする
  showDeploySuccessWithMsg({
    deployerAddress,
    contractObj: {
      RenewableEnergyToken: contractMap.get('RenewableEnergyToken'),
      RenewableEnergyTokenLib: contractMap.get('RenewableEnergyTokenLib'),
      ADMIN: deployerAddress,
    },
  })

  const transferProxyContract = await ethers.getContractAt('TransferProxy', transferProxyAddress)
  printSection({ title: `Add rule to TransferProxy custom contract` })

  await handleTransaction({
    transaction: transferProxyContract.addRule(renewableEnergyToken.target, 0, { nonce: currentNonceDefaultSinger++ }),
  })

  printSection({ title: `Check all registered custom contract addresses` })
  const findAll = await transferProxyContract.findAll()
  console.log(`result: ${findAll}`)
}

export default func
func.tags = ['renewable']
func.dependencies = ['main-contracts']
