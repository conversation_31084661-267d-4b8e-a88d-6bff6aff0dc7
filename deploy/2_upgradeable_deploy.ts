/**
 * Hardhat Deployment Script
 * 1. ネットワーク: 'local'と'main'を'hardhat.config.ts'で設定
 * 2. ウォレット: 'local'の場合、'admin'と'deployer'はデフォルトで設定されるプライベートキーを利用する
 * 3. デプロイライブラリ: 'hardhat-deploy'を利用し、Proxyとして'OpenZeppelinTransparentProxy'を設定
 */
import { showDeployNetwork, showDeploySuccessWithMsg } from '@deploy/common/consoles'
import { deployContractWithSaveABI } from '@deploy/common/deployContractWithSaveABI'
import { getContractInstance } from '@deploy/common/getContractInstance'
import { handleTransaction } from '@deploy/common/handleTransaction'
import { kmsSignerProvider } from '@deploy/common/kmsSignerProvider'
import { saveChainId } from '@deploy/common/saveChainId'
import { ParamType } from 'ethers'
import { ethers } from 'hardhat'
import { DeployFunction } from 'hardhat-deploy/types'

const func: DeployFunction = async () => {
  try {
    await saveChainId()
    console.log('.chainId file created')
  } catch (error) {
    console.error('.chainId file creation error:', error)
  }

  // ethers.js v6のgetContractFactoryを利用してdeployを行う
  // getContractFactory()の引数には送信者を設定するsigner, Link対象コントラクトを設定するlibrariesが存在する
  // Deployの順番は前後順番があるので注意する：(1)Library系(Errorなど) (2)ContractManager (3)Others
  showDeployNetwork({ title: '' })

  // SetContractで利用するContract情報を入れるMap
  const contractMap = new Map()
  const signer = kmsSignerProvider()

  // nonce reset code
  let currentNonce = await ethers.provider.getTransactionCount(await signer.getAddress())
  console.log(`Current nonce for deployer: ${currentNonce}`)

  const deployerAddress = await signer.getAddress()
  console.log(`Deployer address: ${deployerAddress}`)

  // Deploy ContractManager
  const contractManagerContract = await deployContractWithSaveABI({
    contractName: 'ContractManager',
    options: { signer },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy Error
  await deployContractWithSaveABI({
    contractName: 'Error',
    options: { signer },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy ProviderLogicCallLib
  await deployContractWithSaveABI({
    contractName: 'ProviderLogicCallLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy ProviderLogicExecuteLib
  await deployContractWithSaveABI({
    contractName: 'ProviderLogicExecuteLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy IssuerLogicCallLib
  await deployContractWithSaveABI({
    contractName: 'IssuerLogicCallLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy IssuerLogicExecuteLib
  await deployContractWithSaveABI({
    contractName: 'IssuerLogicExecuteLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy ValidatorLogicCallLib
  await deployContractWithSaveABI({
    contractName: 'ValidatorLogicCallLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy ValidatorLogicExecuteLib
  await deployContractWithSaveABI({
    contractName: 'ValidatorLogicExecuteLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy TokenLogicCallLib
  await deployContractWithSaveABI({
    contractName: 'TokenLogicCallLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy TokenLogicExecuteLib
  await deployContractWithSaveABI({
    contractName: 'TokenLogicExecuteLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy AccountLib
  await deployContractWithSaveABI({
    contractName: 'AccountLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy FinancialZoneAccountLib
  await deployContractWithSaveABI({
    contractName: 'FinancialZoneAccountLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy BusinessZoneAccountLib
  await deployContractWithSaveABI({
    contractName: 'BusinessZoneAccountLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy RemigrationLib
  await deployContractWithSaveABI({
    contractName: 'RemigrationLib',
    options: { signer },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Get Contract Instance
  const providerLogicCallLibInstance = await getContractInstance({
    contractName: 'ProviderLogicCallLib',
    contractAddress: contractMap.get('ProviderLogicCallLib'),
    signer,
  })
  const providerLogicExecuteLibInstance = await getContractInstance({
    contractName: 'ProviderLogicExecuteLib',
    contractAddress: contractMap.get('ProviderLogicExecuteLib'),
    signer,
  })
  const IssuerLogicCallLibInstance = await getContractInstance({
    contractName: 'IssuerLogicCallLib',
    contractAddress: contractMap.get('IssuerLogicCallLib'),
    signer,
  })
  const issuerLogicExecuteLibInstance = await getContractInstance({
    contractName: 'IssuerLogicExecuteLib',
    contractAddress: contractMap.get('IssuerLogicExecuteLib'),
    signer,
  })
  const validatorLogicCallLibInstance = await getContractInstance({
    contractName: 'ValidatorLogicCallLib',
    contractAddress: contractMap.get('ValidatorLogicCallLib'),
    signer,
  })
  const validatorLogicExecuteLibInstance = await getContractInstance({
    contractName: 'ValidatorLogicExecuteLib',
    contractAddress: contractMap.get('ValidatorLogicExecuteLib'),
    signer,
  })
  const accountLibInstance = await getContractInstance({
    contractName: 'AccountLib',
    contractAddress: contractMap.get('AccountLib'),
    signer,
  })
  const financialZoneAccountLibInstance = await getContractInstance({
    contractName: 'FinancialZoneAccountLib',
    contractAddress: contractMap.get('FinancialZoneAccountLib'),
    signer,
  })
  const businessZoneAccountLibInstance = await getContractInstance({
    contractName: 'BusinessZoneAccountLib',
    contractAddress: contractMap.get('BusinessZoneAccountLib'),
    signer,
  })
  const tokenLogicCallLibInstance = await getContractInstance({
    contractName: 'TokenLogicCallLib',
    contractAddress: contractMap.get('TokenLogicCallLib'),
    signer,
  })
  const tokenLogicExecuteLibInstance = await getContractInstance({
    contractName: 'TokenLogicExecuteLib',
    contractAddress: contractMap.get('TokenLogicExecuteLib'),
    signer,
  })
  const remigrationLibInstance = await getContractInstance({
    contractName: 'RemigrationLib',
    contractAddress: contractMap.get('RemigrationLib'),
    signer,
  })

  // Deploy AccessCtrl
  await deployContractWithSaveABI({
    contractName: 'AccessCtrl',
    options: {
      signer,
    },
    initialize: {
      args: [contractMap.get('ContractManager'), deployerAddress],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy ProviderStorage
  await deployContractWithSaveABI({
    contractName: 'ProviderStorage',
    options: {
      signer,
      libraries: {
        RemigrationLib: remigrationLibInstance,
      },
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy ProviderLogic
  await deployContractWithSaveABI({
    contractName: 'ProviderLogic',
    options: {
      signer,
      libraries: {
        ProviderLogicCallLib: providerLogicCallLibInstance,
        ProviderLogicExecuteLib: providerLogicExecuteLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager'), contractMap.get('ProviderStorage')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Initialize ProviderStorage after ProviderLogic is deployed
  const providerStorageContract = await getContractInstance({
    contractName: 'ProviderStorage',
    contractAddress: contractMap.get('ProviderStorage'),
    signer,
  })

  await handleTransaction({
    transaction: providerStorageContract.initialize(
      contractMap.get('ContractManager'),
      contractMap.get('ProviderLogic'),
      { nonce: currentNonce++ },
    ),
  })

  // Deploy IssuerStorage
  await deployContractWithSaveABI({
    contractName: 'IssuerStorage',
    options: {
      signer,
      libraries: {
        RemigrationLib: remigrationLibInstance,
      },
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy IssuerLogic
  await deployContractWithSaveABI({
    contractName: 'IssuerLogic',
    options: {
      signer,
      libraries: {
        IssuerLogicCallLib: IssuerLogicCallLibInstance,
        IssuerLogicExecuteLib: issuerLogicExecuteLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager'), contractMap.get('IssuerStorage')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy ValidatorStorage
  await deployContractWithSaveABI({
    contractName: 'ValidatorStorage',
    options: {
      signer,
      libraries: {
        RemigrationLib: remigrationLibInstance,
      },
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy ValidatorLogic
  await deployContractWithSaveABI({
    contractName: 'ValidatorLogic',
    options: {
      signer,
      libraries: {
        ValidatorLogicCallLib: validatorLogicCallLibInstance,
        ValidatorLogicExecuteLib: validatorLogicExecuteLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager'), contractMap.get('ValidatorStorage')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Initialize ValidatorStorage after ValidatorLogic is deployed
  const validatorStorageContract = await getContractInstance({
    contractName: 'ValidatorStorage',
    contractAddress: contractMap.get('ValidatorStorage'),
    signer,
  })
  await handleTransaction({
    transaction: validatorStorageContract.initialize(
      contractMap.get('ContractManager'),
      contractMap.get('ValidatorLogic'),
      { nonce: currentNonce++ },
    ),
  })

  // Initialize IssuerStorage after IssuerLogic is deployed
  const issuerStorageContract = await getContractInstance({
    contractName: 'IssuerStorage',
    contractAddress: contractMap.get('IssuerStorage'),
    signer,
  })
  await handleTransaction({
    transaction: issuerStorageContract.initialize(contractMap.get('ContractManager'), contractMap.get('IssuerLogic'), {
      nonce: currentNonce++,
    }),
  })

  // Deploy Account
  await deployContractWithSaveABI({
    contractName: 'Account',
    options: {
      signer,
      libraries: {
        AccountLib: accountLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy FinancialZoneAccount
  await deployContractWithSaveABI({
    contractName: 'FinancialZoneAccount',
    options: {
      signer,
      libraries: {
        FinancialZoneAccountLib: financialZoneAccountLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy BusinessZoneAccount
  await deployContractWithSaveABI({
    contractName: 'BusinessZoneAccount',
    options: {
      signer,
      libraries: {
        BusinessZoneAccountLib: businessZoneAccountLibInstance,
        RemigrationLib: remigrationLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy TokenStorage
  await deployContractWithSaveABI({
    contractName: 'TokenStorage',
    options: {
      signer,
      libraries: {
        RemigrationLib: remigrationLibInstance,
      },
    },
    contractMap,
    overrides: { nonce: currentNonce++ },
  })

  // Deploy TokenLogic
  await deployContractWithSaveABI({
    contractName: 'TokenLogic',
    options: {
      signer,
      libraries: {
        TokenLogicCallLib: tokenLogicCallLibInstance,
        TokenLogicExecuteLib: tokenLogicExecuteLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager'), contractMap.get('TokenStorage')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Initialize TokenStorage after TokenLogic is deployed
  const tokenStorageContract = await getContractInstance({
    contractName: 'TokenStorage',
    contractAddress: contractMap.get('TokenStorage'),
    signer,
  })
  await handleTransaction({
    transaction: tokenStorageContract.initialize(contractMap.get('ContractManager'), contractMap.get('TokenLogic'), {
      nonce: currentNonce++,
    }),
  })

  // Deploy IBCToken
  await deployContractWithSaveABI({
    contractName: 'IBCToken',
    options: {
      signer,
      libraries: {
        TokenLogicCallLib: tokenLogicCallLibInstance,
        TokenLogicExecuteLib: tokenLogicExecuteLibInstance,
      },
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy FinancialCheck
  await deployContractWithSaveABI({
    contractName: 'FinancialCheck',
    options: {
      signer,
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy TransferProxy
  await deployContractWithSaveABI({
    contractName: 'TransferProxy',
    options: {
      signer,
    },
    initialize: {
      args: [contractMap.get('ContractManager'), contractMap.get('TokenLogic')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy RemigrationRestore
  await deployContractWithSaveABI({
    contractName: 'RemigrationRestore',
    options: {
      signer,
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  // Deploy RemigrationBackup
  await deployContractWithSaveABI({
    contractName: 'RemigrationBackup',
    options: {
      signer,
    },
    initialize: {
      args: [contractMap.get('ContractManager')],
    },
    saveABIflag: true,
    contractMap,
    overrides: { nonce: currentNonce++ },
    initializeOverrides: { nonce: currentNonce++ },
  })

  console.log('*************************************')
  console.log(`* Set contract addresses to ContractManager *`)
  console.log('*************************************')
  console.log('Setting all contract addresses to ContractManager...')

  const deadline = (await Math.floor(Date.now() / 1000)) + 10
  const types: (string | ParamType)[] = [
    ParamType.from({
      type: 'tuple',
      components: [
        { type: 'address', name: 'ctrlAddress' },
        { type: 'address', name: 'providerAddress' },
        { type: 'address', name: 'issuerAddress' },
        { type: 'address', name: 'validatorAddress' },
        { type: 'address', name: 'validatorStorageAddress' },
        { type: 'address', name: 'accountAddress' },
        { type: 'address', name: 'financialZoneAccountAddress' },
        { type: 'address', name: 'businessZoneAccountAddress' },
        { type: 'address', name: 'tokenAddress' },
        { type: 'address', name: 'ibcTokenAddress' },
        { type: 'address', name: 'financialCheckAddress' },
        { type: 'address', name: 'transferProxyAddress' },
        { type: 'address', name: 'issuerStorageAddress' },
        { type: 'address', name: 'tokenStorageAddress' },
        { type: 'address', name: 'providerStorageAddress' },
      ],
    }),
    'uint256',
  ]
  const jsonAddress = {
    ctrlAddress: contractMap.get('AccessCtrl'),
    providerAddress: contractMap.get('ProviderLogic'),
    issuerAddress: contractMap.get('IssuerLogic'),
    validatorAddress: contractMap.get('ValidatorLogic'),
    validatorStorageAddress: contractMap.get('ValidatorStorage'),
    accountAddress: contractMap.get('Account'),
    financialZoneAccountAddress: contractMap.get('FinancialZoneAccount'),
    businessZoneAccountAddress: contractMap.get('BusinessZoneAccount'),
    tokenAddress: contractMap.get('TokenLogic'),
    ibcTokenAddress: contractMap.get('IBCToken'),
    financialCheckAddress: contractMap.get('FinancialCheck'),
    transferProxyAddress: contractMap.get('TransferProxy'),
    issuerStorageAddress: contractMap.get('IssuerStorage'),
    tokenStorageAddress: contractMap.get('TokenStorage'),
    providerStorageAddress: contractMap.get('ProviderStorage'),
  }

  const signatureBytes = await signer.sign(types, [jsonAddress, deadline])

  await handleTransaction({
    transaction: contractManagerContract.setContracts(jsonAddress, deadline, signatureBytes, { nonce: currentNonce++ }),
  })

  // Contract set to manageContract情報をFormatしてPrintする
  showDeploySuccessWithMsg({
    deployerAddress,
    contractObj: {
      ContractManager: contractMap.get('ContractManager'),
      AccessCtrl: contractMap.get('AccessCtrl'),
      ProviderLogic: contractMap.get('ProviderLogic'),
      ProviderStorage: contractMap.get('ProviderStorage'),
      IssuerLogic: contractMap.get('IssuerLogic'),
      IssuerStorage: contractMap.get('IssuerStorage'),
      ValidatorLogic: contractMap.get('ValidatorLogic'),
      ValidatorStorage: contractMap.get('ValidatorStorage'),
      Account: contractMap.get('Account'),
      FinancialZoneAccount: contractMap.get('FinancialZoneAccount'),
      BusinessZoneAccount: contractMap.get('BusinessZoneAccount'),
      TokenLogic: contractMap.get('TokenLogic'),
      TokenStorage: contractMap.get('TokenStorage'),
      IBCToken: contractMap.get('IBCToken'),
      FinancialCheck: contractMap.get('FinancialCheck'),
      TransferProxy: contractMap.get('TransferProxy'),
      ErrorLib: contractMap.get('Error'),
      ProviderLogicCallLib: contractMap.get('ProviderLogicCallLib'),
      ProviderLogicExecuteLib: contractMap.get('ProviderLogicExecuteLib'),
      IssuerLogicCallLib: contractMap.get('IssuerLogicCallLib'),
      IssuerLogicExecuteLib: contractMap.get('IssuerLogicExecuteLib'),
      ValidatorLogicCallLib: contractMap.get('ValidatorLogicCallLib'),
      ValidatorLogicExecuteLib: contractMap.get('ValidatorLogicExecuteLib'),
      AccountLib: contractMap.get('AccountLib'),
      FinancialZoneAccountLib: contractMap.get('FinancialZoneAccountLib'),
      BusinessZoneAccountLib: contractMap.get('BusinessZoneAccountLib'),
      TokenLogicCallLib: contractMap.get('TokenLogicCallLib'),
      TokenLogicExecuteLib: contractMap.get('TokenLogicExecuteLib'),
      ADMIN: deployerAddress,
    },
  })

  // chainIdを保存
  await saveChainId()
}

export default func
func.tags = ['main-contracts']
