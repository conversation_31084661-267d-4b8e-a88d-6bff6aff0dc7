import '@nomicfoundation/hardhat-chai-matchers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ContractManagerInstance, ProviderInstance, ProviderStorageInstance } from '@test/common/types'
import { ProviderContractType } from '@test/Provider/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('initialize()', () => {
  let provider: ProviderInstance
  let contractManager: ContractManagerInstance
  let providerStorage: ProviderStorageInstance

  const setupFixture = async () => {
    ;({ provider, contractManager, providerStorage } = await contractFixture<ProviderContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('should revert when initialized', async () => {
      await expect(
        provider.initialize(await contractManager.getAddress(), await providerStorage.getAddress()),
      ).to.be.revertedWith(ERR.INITIALIZER.ALREADY_INIT)
    })
  })
})
