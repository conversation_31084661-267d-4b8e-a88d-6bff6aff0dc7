import '@nomicfoundation/hardhat-chai-matchers'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ContractManagerInstance, TokenInstance, TokenStorageInstance } from '@test/common/types'
import { TokenContractType } from '@test/Token/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('initialize()', () => {
  let token: TokenInstance
  let contractManager: ContractManagerInstance
  let tokenStorage: TokenStorageInstance

  const setupFixture = async () => {
    ;({ token, contractManager, tokenStorage } = await contractFixture<TokenContractType>())
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('should revert when initialized', async () => {
      await expect(
        token.initialize(await contractManager.getAddress(), await tokenStorage.getAddress()),
      ).to.be.revertedWith(ERR.INITIALIZER.ALREADY_INIT)
    })
  })
})
