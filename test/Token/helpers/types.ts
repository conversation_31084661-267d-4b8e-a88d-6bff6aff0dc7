import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccessCtrlInstance,
  AccountInstance,
  ApproveOption,
  BurnCancelOption,
  BurnOption,
  BusinessZoneAccountInstance,
  ContractCallOption,
  ContractManagerInstance,
  CustomTransferOption,
  EventReturnType,
  IBCTokenInstance,
  IssuerInstance,
  MintOption,
  ProviderInstance,
  ProviderStorageInstance,
  SetTokenEnabledOption,
  TokenInstance,
  TokenStorageInstance,
  TransferableMock1Instance,
  TransferableMock2Instance,
  TransferableMock3Instance,
  TransferProxyInstance,
  TransferSingleOption,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { ContractTransactionResponse } from 'ethers'
import { PromiseType } from 'utility-types'

export type TokenContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  providerStorage: ProviderStorageInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  tokenStorage: TokenStorageInstance
  ibcToken: IBCTokenInstance
  transferProxy: TransferProxyInstance
  contractManager: ContractManagerInstance
  customTransfer1: TransferableMock1Instance
  customTransfer2: TransferableMock2Instance
  customTransfer3: TransferableMock3Instance
  accessCtrl: AccessCtrlInstance
  businessZoneAccount: BusinessZoneAccountInstance
}

type TokenType = { token: TokenInstance }

type BaseTransferType = {
  token: TokenInstance
  accounts: SignerWithAddress[]
  amount: number
  miscValue1: string
  miscValue2: string
}

type BaseAmountType = {
  token: TokenInstance
  accounts: SignerWithAddress[]
  amount: number
}

export type FuncParamsType = {
  version: TokenType
  getAllowance: TokenType & { prams: Parameters<TokenInstance['getAllowance']> }
  getAllowanceList: TokenType & {
    prams: Parameters<TokenInstance['getAllowanceList']>
  }
  getBalanceList: TokenType & {
    prams: Parameters<TokenInstance['getBalanceList']>
  }
  hasToken: TokenType & { prams: Parameters<TokenInstance['hasToken']> }
  checkApprove: TokenType & {
    validatorId: string
    ownerId: string
    spenderId: string
    amount: number
    sigInfo: PromiseType<ReturnType<typeof utils.siginfoGenerator>>
    options?: Partial<
      {
        accountSignature: Parameters<TokenInstance['checkApprove']>[4]
      } & ContractCallOption
    >
  }
  getToken: TokenType & { prams: Parameters<TokenInstance['getToken']> }
  setTokenEnabled: TokenType & {
    accounts: SignerWithAddress[]
    enabled: boolean
    options?: Partial<SetTokenEnabledOption & ContractCallOption>
  }
  approve: BaseAmountType & {
    spenderId: string
    options?: Partial<ApproveOption & ContractCallOption>
  }
  mint: BaseAmountType & {
    options?: Partial<MintOption & ContractCallOption>
  }
  burn: BaseAmountType & {
    options?: Partial<BurnOption & ContractCallOption>
  }
  burnCancel: BaseAmountType & {
    blockTimestamp: string | number
    options?: Partial<BurnCancelOption & ContractCallOption>
  }
  transferSingle: BaseTransferType & {
    options?: Partial<TransferSingleOption>
  }
  customTransfer: BaseTransferType & {
    options?: Partial<CustomTransferOption>
  }
  getTokenAll: TokenType
  setTokenAll: TokenType & {
    prams: any
    options: Partial<ContractCallOption>
  }
}

export type FuncReturnType = {
  version: string
  getAllowance: EventReturnType['Token']['GetAllowance']
  getAllowanceList: EventReturnType['Token']['GetAllowanceList']
  getBalanceList: EventReturnType['Token']['GetBalanceList']
  hasToken: EventReturnType['Token']['HasToken']
  checkApprove: EventReturnType['FinancialCheck']['CheckApprove']
  getToken: EventReturnType['Token']['GetToken']
  setTokenEnabled: ContractTransactionResponse
  approve: ContractTransactionResponse
  mint: ContractTransactionResponse
  burn: ContractTransactionResponse
  burnCancel: ContractTransactionResponse
  transferSingle: ContractTransactionResponse
  customTransfer: ContractTransactionResponse
  getTokenAll: EventReturnType['Token']['GetToken']
  setTokenAll: ContractTransactionResponse
}

export type TokenFunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<FuncReturnType[K]>
}
