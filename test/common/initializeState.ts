import * as privateKey from '@/privateKey'
import {} from '@/types/hardhat'
import '@nomicfoundation/hardhat-ethers'
import hre, { ethers } from 'hardhat'
import { getDeadline } from './utils'
import { Provider } from '@contracts/Provider'

export const deployFixture = async () => {
  const [admin] = await hre.ethers.getSigners()

  // Deploy storage logic
  const ProviderLogicCallLib = await (await hre.ethers.getContractFactory('ProviderLogicCallLib')).deploy()
  const ProviderLogicExecuteLib = await (await hre.ethers.getContractFactory('ProviderLogicExecuteLib')).deploy()
  const IssuerLogicCallLib = await (await hre.ethers.getContractFactory('IssuerLogicCallLib')).deploy()
  const IssuerLogicExecuteLib = await (await hre.ethers.getContractFactory('IssuerLogicExecuteLib')).deploy()
  const ValidatorLogicCallLib = await (await hre.ethers.getContractFactory('ValidatorLogicCallLib')).deploy()
  const ValidatorLogicExecuteLib = await (await hre.ethers.getContractFactory('ValidatorLogicExecuteLib')).deploy()
  const AccountLib = await (await hre.ethers.getContractFactory('AccountLib')).deploy()
  const FinancialZoneAccountLib = await (await hre.ethers.getContractFactory('FinancialZoneAccountLib')).deploy()
  const BusinessZoneAccountLib = await (await hre.ethers.getContractFactory('BusinessZoneAccountLib')).deploy()
  const TokenLogicCallLib = await (await hre.ethers.getContractFactory('TokenLogicCallLib')).deploy()
  const TokenLogicExecuteLib = await (await hre.ethers.getContractFactory('TokenLogicExecuteLib')).deploy()
  const renewableEnergyTokenLogicCallLib = await (
    await hre.ethers.getContractFactory('RenewableEnergyTokenLogicCallLib')
  ).deploy()
  const renewableEnergyTokenLogicExecuteLib = await (
    await hre.ethers.getContractFactory('RenewableEnergyTokenLogicExecuteLib')
  ).deploy()

  // Deploy contract
  const remigrationLib = await (await hre.ethers.getContractFactory('RemigrationLib')).deploy()
  const accessCtrl = await (await hre.ethers.getContractFactory('AccessCtrl')).deploy()
  const account = await (
    await hre.ethers.getContractFactory('Account', {
      libraries: {
        AccountLib: await AccountLib.getAddress(),
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const financialZoneAccount = await (
    await hre.ethers.getContractFactory('FinancialZoneAccount', {
      libraries: {
        FinancialZoneAccountLib: await FinancialZoneAccountLib.getAddress(),
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const businessZoneAccount = await (
    await hre.ethers.getContractFactory('BusinessZoneAccount', {
      libraries: {
        BusinessZoneAccountLib: await BusinessZoneAccountLib.getAddress(),
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const contractManager = await (await hre.ethers.getContractFactory('ContractManager')).deploy()
  const issuerStorage = await (
    await hre.ethers.getContractFactory('IssuerStorage', {
      libraries: {
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const issuer = await (
    await hre.ethers.getContractFactory('IssuerLogic', {
      libraries: {
        IssuerLogicCallLib: await IssuerLogicCallLib.getAddress(),
        IssuerLogicExecuteLib: await IssuerLogicExecuteLib.getAddress(),
      },
    })
  ).deploy()

  const providerStorage = await (
    await hre.ethers.getContractFactory('ProviderStorage', {
      libraries: {
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const provider = (await (
    await hre.ethers.getContractFactory('ProviderLogic', {
      libraries: {
        ProviderLogicCallLib: await ProviderLogicCallLib.getAddress(),
        ProviderLogicExecuteLib: await ProviderLogicExecuteLib.getAddress(),
      },
    })
  ).deploy()) as unknown as Provider
  const tokenStorage = await (
    await hre.ethers.getContractFactory('TokenStorage', {
      libraries: {
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()
  const token = await (
    await hre.ethers.getContractFactory('TokenLogic', {
      libraries: {
        TokenLogicCallLib: await TokenLogicCallLib.getAddress(),
        TokenLogicExecuteLib: await TokenLogicExecuteLib.getAddress(),
      },
    })
  ).deploy()
  const ibcToken = await (
    await hre.ethers.getContractFactory('IBCToken', {
      libraries: {
        TokenLogicCallLib: await TokenLogicCallLib.getAddress(),
        TokenLogicExecuteLib: await TokenLogicExecuteLib.getAddress(),
      },
    })
  ).deploy()
  const financialCheck = await (await hre.ethers.getContractFactory('FinancialCheck')).deploy()
  const renewableEnergyToken = await (
    await hre.ethers.getContractFactory('RenewableEnergyTokenLogic', {
      libraries: {
        RenewableEnergyTokenLogicCallLib: await renewableEnergyTokenLogicCallLib.getAddress(),
        RenewableEnergyTokenLogicExecuteLib: await renewableEnergyTokenLogicExecuteLib.getAddress(),
      },
    })
  ).deploy()
  const renewableEnergyTokenStorage = await (
    await hre.ethers.getContractFactory('RenewableEnergyTokenStorage')
  ).deploy()
  const validatorStorage = await (
    await hre.ethers.getContractFactory('ValidatorStorage', {
      libraries: {
        RemigrationLib: await remigrationLib.getAddress(),
      },
    })
  ).deploy()

  const validator = await (
    await hre.ethers.getContractFactory('ValidatorLogic', {
      libraries: {
        ValidatorLogicCallLib: await ValidatorLogicCallLib.getAddress(),
        ValidatorLogicExecuteLib: await ValidatorLogicExecuteLib.getAddress(),
      },
    })
  ).deploy()
  const stringUtilsMock = await (await hre.ethers.getContractFactory('StringUtilsMock')).deploy()
  const transferProxy = await (await hre.ethers.getContractFactory('TransferProxy')).deploy()
  const customTransfer1 = await (await hre.ethers.getContractFactory('TransferableMock1')).deploy()
  const customTransfer2 = await (await hre.ethers.getContractFactory('TransferableMock2')).deploy()
  const customTransfer3 = await (await hre.ethers.getContractFactory('TransferableMock3')).deploy()
  const oracle = await (await hre.ethers.getContractFactory('Oracle')).deploy()
  const remigrationRestore = await (await hre.ethers.getContractFactory('RemigrationRestore')).deploy()
  const remigrationBackup = await (await hre.ethers.getContractFactory('RemigrationBackup')).deploy()

  // contract-ibc
  const ibcHandler = await (await hre.ethers.getContractFactory('IBCHandlerMock')).deploy()
  const accountSyncBridge = await (await hre.ethers.getContractFactory('AccountSyncBridge')).deploy()
  const balanceSyncBridge = await (await hre.ethers.getContractFactory('BalanceSyncBridge')).deploy()
  const jpyTokenTransferBridge = await (await hre.ethers.getContractFactory('JPYTokenTransferBridge')).deploy()
  const ibcTokenMock = await (await hre.ethers.getContractFactory('IBCTokenMock')).deploy()
  const providerMock = await (await hre.ethers.getContractFactory('ProviderMock')).deploy()
  const validatorMock = await (await hre.ethers.getContractFactory('ValidatorMock')).deploy()
  const accountMock = await (await hre.ethers.getContractFactory('AccountMock')).deploy()
  const accessCtrlMock = await (await hre.ethers.getContractFactory('AccessCtrlMock')).deploy()
  const businessZoneAccountMock = await (await hre.ethers.getContractFactory('BusinessZoneAccountMock')).deploy()

  // Initialize
  const contractManagerAddress = await contractManager.getAddress()
  const accessCtrlAddress = await accessCtrl.getAddress()
  const providerAddress = await provider.getAddress()
  const issuerAddress = await issuer.getAddress()
  const validatorAddress = await validator.getAddress()
  const validatorStorageAddress = await validatorStorage.getAddress()
  const issuerStorageAddress = await issuerStorage.getAddress()
  const providerStorageAddress = await providerStorage.getAddress()
  const accountAddress = await account.getAddress()
  const financialZoneAccountAddress = await financialZoneAccount.getAddress()
  const businessZoneAccountAddress = await businessZoneAccount.getAddress()
  const tokenAddress = await token.getAddress()
  const tokenStorageAddress = await tokenStorage.getAddress()
  const ibcTokenAddress = await ibcToken.getAddress()
  const financialCheckAddress = await financialCheck.getAddress()
  const transferProxyAddress = await transferProxy.getAddress()
  const renewableEnergyTokenAddress = await renewableEnergyToken.getAddress()
  const renewableEnergyTokenStorageAddress = await renewableEnergyTokenStorage.getAddress()

  await renewableEnergyToken.getAddress()
  await renewableEnergyTokenStorage.getAddress()
  await customTransfer1.getAddress()
  await customTransfer2.getAddress()
  await customTransfer3.getAddress()
  await oracle.getAddress()
  await remigrationLib.getAddress()
  await remigrationRestore.getAddress()
  await remigrationBackup.getAddress()

  const ibcHandlerAddress = await ibcHandler.getAddress()
  const ibcTokenMockAddress = await ibcTokenMock.getAddress()
  const providerMockAddress = await providerMock.getAddress()
  const validatorMockAddress = await validatorMock.getAddress()
  const accountMockAddress = await accountMock.getAddress()
  const accessCtrlMockAddress = await accessCtrlMock.getAddress()
  const businessZoneAccountMockAddress = await businessZoneAccountMock.getAddress()

  await accountSyncBridge.getAddress()
  await balanceSyncBridge.getAddress()
  await jpyTokenTransferBridge.getAddress()
  await stringUtilsMock.getAddress()

  await contractManager.initialize()
  await accessCtrl.initialize(contractManagerAddress, await admin.getAddress())
  await provider.initialize(contractManagerAddress, providerStorageAddress)
  await providerStorage.initialize(contractManagerAddress, providerAddress)
  await issuerStorage.initialize(contractManagerAddress, issuerAddress)
  await issuer.initialize(contractManagerAddress, issuerStorageAddress)
  await validatorStorage.initialize(contractManagerAddress, validatorAddress)
  await validator.initialize(contractManagerAddress, validatorStorageAddress)
  await account.initialize(contractManagerAddress)
  await financialZoneAccount.initialize(contractManagerAddress)
  await businessZoneAccount.initialize(contractManagerAddress)
  await token.initialize(contractManagerAddress, tokenStorageAddress)
  await tokenStorage.initialize(contractManagerAddress, tokenAddress)
  await ibcToken.initialize(contractManagerAddress)
  await financialCheck.initialize(contractManagerAddress)
  await renewableEnergyTokenStorage.initialize(contractManagerAddress, renewableEnergyTokenAddress)
  await renewableEnergyToken.initialize(renewableEnergyTokenStorageAddress, tokenAddress, contractManagerAddress)
  await transferProxy.initialize(contractManagerAddress, tokenAddress)
  await customTransfer1.initialize(tokenAddress)
  await customTransfer2.initialize(tokenAddress)
  await customTransfer3.initialize(tokenAddress)
  await oracle.initialize()
  await remigrationRestore.initialize(contractManagerAddress)
  await remigrationBackup.initialize(contractManagerAddress)

  await accountSyncBridge.initialize(
    ibcHandlerAddress,
    providerMockAddress,
    validatorMockAddress,
    accessCtrlMockAddress,
    businessZoneAccountMockAddress,
    ibcTokenMockAddress,
  )
  await balanceSyncBridge.initialize(ibcHandlerAddress, ibcTokenMockAddress, accountMockAddress, accessCtrlMockAddress)
  await jpyTokenTransferBridge.initialize(ibcHandlerAddress, ibcTokenMockAddress, accessCtrlMockAddress)

  const jsonAddresses = {
    ctrlAddress: accessCtrlAddress,
    providerAddress: providerAddress,
    issuerAddress: issuerAddress,
    validatorAddress: validatorAddress,
    validatorStorageAddress: validatorStorageAddress,
    accountAddress: accountAddress,
    financialZoneAccountAddress: financialZoneAccountAddress,
    businessZoneAccountAddress: businessZoneAccountAddress,
    tokenAddress: tokenAddress,
    ibcTokenAddress: ibcTokenAddress,
    financialCheckAddress: financialCheckAddress,
    transferProxyAddress: transferProxyAddress,
    issuerStorageAddress: issuerStorageAddress,
    tokenStorageAddress: tokenStorageAddress,
    providerStorageAddress: providerStorageAddress,
  }

  const deadline = await getDeadline()
  const testPrivateKey = privateKey.key[0]
  const sig = privateKey.sig(
    testPrivateKey,
    [
      {
        type: 'tuple',
        components: [
          { type: 'address', name: 'ctrlAddress' },
          { type: 'address', name: 'providerAddress' },
          { type: 'address', name: 'issuerAddress' },
          { type: 'address', name: 'validatorAddress' },
          { type: 'address', name: 'validatorStorageAddress' },
          { type: 'address', name: 'accountAddress' },
          { type: 'address', name: 'financialZoneAccountAddress' },
          { type: 'address', name: 'businessZoneAccountAddress' },
          { type: 'address', name: 'tokenAddress' },
          { type: 'address', name: 'ibcTokenAddress' },
          { type: 'address', name: 'financialCheckAddress' },
          { type: 'address', name: 'transferProxyAddress' },
          { type: 'address', name: 'issuerStorageAddress' },
          { type: 'address', name: 'tokenStorageAddress' },
          { type: 'address', name: 'providerStorageAddress' },
        ],
      },
      'uint256',
    ],
    [jsonAddresses, deadline],
  )

  await contractManager.setContracts(jsonAddresses, deadline, sig[0])

  const accounts = await ethers.getSigners()

  return {
    accounts,
    accessCtrl,
    account,
    financialZoneAccount,
    businessZoneAccount,
    contractManager,
    issuer,
    issuerStorage,
    provider,
    providerStorage,
    token,
    tokenStorage,
    ibcToken,
    financialCheck,
    renewableEnergyToken,
    renewableEnergyTokenStorage,
    validator,
    validatorStorage,
    transferProxy,
    customTransfer1,
    customTransfer2,
    customTransfer3,
    oracle,
    remigrationBackup,
    remigrationRestore,
    ibcHandler,
    accountSyncBridge,
    balanceSyncBridge,
    jpyTokenTransferBridge,
    ibcTokenMock,
    providerMock,
    validatorMock,
    accountMock,
    accessCtrlMock,
    businessZoneAccountMock,
    stringUtilsMock,
  }
}
