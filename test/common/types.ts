import { BytesLike } from '@ethersproject/bytes'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BigNumberish } from 'ethers'
import { PromiseType } from 'utility-types'
import privateKey from '@/privateKey'
import {
  AccessCtrl,
  AccessCtrlMock,
  Account,
  AccountMock,
  AccountSyncBridge,
  BalanceSyncBridge,
  BusinessZoneAccount,
  BusinessZoneAccountMock,
  ContractManager,
  FinancialCheck,
  FinancialZoneAccount,
  IBCHandlerMock,
  IBCToken,
  IBCTokenMock,
  IssuerLogic,
  IssuerStorage,
  JPYTokenTransferBridge,
  Oracle,
  Provider,
  ProviderStorage,
  ProviderMock,
  RemigrationBackup,
  RemigrationRestore,
  RenewableEnergyToken,
  StringUtilsMock,
  Token,
  TokenStorage,
  TransferableMock1,
  TransferableMock2,
  TransferableMock3,
  TransferProxy,
  Validator,
  ValidatorMock,
  ValidatorStorage,
} from '@/types'

// オブジェクトが余分なプロパティを含んでいないかチェックするための型定義(https://stackoverflow.com/questions/********/forcing-excess-property-checking-on-variable-passed-to-typescript-function)
export type StrictPropertyCheck<T, TExpected, TError> = T &
  (Exclude<keyof T, keyof TExpected> extends never ? {} : TError)

type CheckEventReturnType = {
  success: boolean
  err: string
}
type ProviderAll = {
  providerId: string
  providerData: ProviderData
  providerEoa: string
  zoneData: ZoneData
}
type IssuerAll = {
  issuerId: string
  bankCode: number | string
  role: string
  name: string
  enabled: boolean
  issuerIdExistence: boolean
  issuerEoa: string
  issuerAccountExistence: Array<IssuerExistence>
}
type IssuerExistence = {
  accountId: string
  accountIdExistenceByIssuerId: boolean
}
type AccountAll = {
  accountId: string
  accountName: string
  accountStatus: string
  zoneIds: number[]
  balance: number | string
  reasonCode: string
  appliedAt: number | string
  registeredAt: number | string
  terminatingAt: number | string
  terminatedAt: number | string
  validatorId: string
  accountIdExistence: boolean
  accountEoa: string
  accountApprovalAll: AccountApprovalAll[]
}
type AccountApprovalAll = {
  spanderId: string
  spenderAccountName: string
  allowanceAmount: number | string
  approvedAt: number | string
}
type FinAccountsAll = {
  accountId: string
  financialZoneAccountData: FinancialZoneAccountData
}
type FinancialZoneAccountData = {
  mintLimit: number | string
  burnLimit: number | string
  chargeLimit: number | string
  dischargeLimit: number | string
  transferLimit: number | string
  cumulativeLimit: number | string
  cumulativeAmount: number | string
  cumulativeDate: number | string
  cumulativeTransactionLimits: CumulativeTransactionLimits
}
type BusinessZoneAccountData = {
  accountName: string
  zoneName: string
  zoneId: number | string
  balance: number | string
  accountStatus: string
  appliedAt: number | string
  registeredAt: number | string
  terminatingAt: number | string
  terminatedAt: number | string
}
type BizAccountsAll = {
  accountId: string
  bizAccountsByZoneId: BizAccountsByZoneId[]
}
type BizAccountsByZoneId = {
  zoneId: number | string
  businessZoneAccountData: BizAccountData
  accountIdExistenceByZoneId: boolean
}
type BizAccountData = {
  accountName: string
  balance: number | string
  accountStatus: string
  appliedAt: number | string
  registeredAt: number | string
  terminatingAt: number | string
  terminatedAt: number | string
}
type ValidatorAllType = {
  validatorId: string
  name: string
  issuerId: string
  role: string
  validatorAccountId: string
  enabled: boolean
  validatorIdExistence: boolean
  issuerIdLinkedFlag: boolean
  validatorEoa: string
  validAccountExistence: {
    accountId: string
    accountIdExistenceByValidatorId: boolean
  }
}
type TokenAll = {
  tokenId: string
  name: string
  symbol: string
  totalSupply: number | string
  enabled: boolean
}
type BizAccountsAllType = {
  accountId: string
  bizAccountsByZoneId: {
    zoneId: number
    accountIdExistenceByZoneId: boolean
    accountName: string
    balance: number | string
    accountStatus: string
  }
}
type OracleEventReturnType = {
  GetBatch: PromiseType<ReturnType<OracleInstance['getBatch']>>
  Get: PromiseType<ReturnType<OracleInstance['get']>>
}
type ProviderData = {
  role: string
  name: string
  zoneId: number | string
  enabled: boolean
}
type ZoneData = {
  zoneId: number | string
  zoneName: string
  availableIssuerIds: string[]
}

export type RenewableEnergyTokenAll = {
  tokenId: string
  renewableEnergyTokenData: StructType['RenewableEnergyTokenDataType']
}

export type TokenIdsByAccountIdAll = {
  accountId: string
  tokenIds: string[]
}

export type EventReturnType = {
  Provider: {
    GetProvider: {
      providerId: string
      zoneId: number | string
      zoneName: string
      err: string
    }
    GetProviderAll: {
      providerId: string
      providerData: ProviderData
      providerEoa: string
      zoneData: ZoneData[]
    }
    GetZone: {
      zoneId: number
      zoneName: string
      err: string
    }
    GetZoneName: string
    GetProviderCount: number
    GetToken: {
      tokenId: string
      name: string
      symbol: string
      totalSupply: number
      enabled: boolean
      err: string
    }
    GetTokenId: {
      tokenId: string
      err: string
    }
    GetAvailableIssuerIds: string[]
    HasToken: CheckEventReturnType
    HasProvider: CheckEventReturnType
    CheckRole: {
      has: boolean
      err: string
    }
    CheckAvailableIssuerIds: {
      success: boolean
      err: string
    }
  }
  Validator: {
    HasValidator: CheckEventReturnType
    GetAccount: {
      accountData: StructType['AccountDataType']
      err: string
    }
    GetDestinationAccount: {
      accountName: string
      err: string
    }
    GetValidatorList: {
      validators: {
        validatorId: string
        name: string
        issuerId: string
      }[]
      totalCount: number
      err: string
    }
    GetValidator: {
      name: string
      issuerId: string
      err: string
    }
    GetAccountAll: {
      accountDataAll: {
        accountName: string
        accountStatus: string
        balance: number | string
        reasonCode: number | string
        appliedAt: number | string
        registeredAt: number | string
        terminatingAt: number | string
        terminatedAt: number | string
        mintLimit: number | string
        burnLimit: number | string
        chargeLimit: number | string
        dischargeLimit: number | string
        transferLimit: number | string
        cumulativeLimit: number | string
        cumulativeAmount: number | string
        cumulativeDate: number | string
        cumulativeTransactionLimits: CumulativeTransactionLimits
        businessZoneAccounts: BusinessZoneAccountData[]
      }
      err: string
    }
    GetAccountAllList: {
      accounts: {
        accountId: string
        accountDataAll: {
          accountName: string
          accountStatus: string
          balance: number | string
          reasonCode: number | string
          appliedAt: number | string
          registeredAt: number | string
          terminatingAt: number | string
          terminatedAt: number | string
          mintLimit: number | string
          burnLimit: number | string
          chargeLimit: number | string
          dischargeLimit: number | string
          transferLimit: number | string
          cumulativeLimit: number | string
          cumulativeAmount: number | string
          cumulativeDate: number | string
          cumulativeTransactionLimits: CumulativeTransactionLimits
          businessZoneAccounts: BusinessZoneAccountData[]
        }
      }[]
      totalCount: number
      err: string
    }
    GetAccountList: {
      accounts: {
        accountId: string
        accountName: string
        balance: number | string
        accountStatus: string
        reasonCode: number | string
        appliedAt: number | string
        registeredAt: number | string
        terminatingAt: number | string
        terminatedAt: number | string
      }[]
      totalCount: number
      err: string
    }
    GetZoneByAccountId: {
      zones: {
        zoneId: number | string
        zoneName: string
      }[]
      err: string
    }
    HasAccount: CheckEventReturnType
    GetValidatorCount: number
    GetValidatorId: {
      validatorId: string
      err: string
    }
    GetValidatorAccountId: {
      accountId: string
      err: string
    }
    HasValidatorRole: CheckEventReturnType
    GetValidatorAll: ValidatorAllType
    GetAccountLimit: {
      mintLimit: number | string
      burnLimit: number | string
      chargeLimit: number | string
      dischargeLimit: number | string
      transferLimit: number | string
      cumulativeLimit: number | string
      cumulativeAmount: number | string
      cumulativeDate: number | string
      cumulativeTransactionLimits: {
        cumulativeMintLimit: number | string
        cumulativeMintAmount: number | string
        cumulativeBurnLimit: number | string
        cumulativeBurnAmount: number | string
        cumulativeChargeLimit: number | string
        cumulativeChargeAmount: number | string
        cumulativeDischargeLimit: number | string
        cumulativeDischargeAmount: number | string
        cumulativeTransferLimit: number | string
        cumulativeTransferAmount: number | string
      }
      err: string
    }
  }
  Issuer: {
    GetIssuerList: {
      issuers: {
        issuerId: string
        bankCode: number | string
        name: string
      }[]
      totalCount: number
      err: string
    }
    GetIssuer: {
      name: string
      bankCode: number
      enabled: boolean
      err: string
    }
    GetIssuerId: {
      issuerId: string
      err: string
    }
    GetIssuerCount: number
    HasIssuer: CheckEventReturnType
    CheckRole: {
      has: boolean
      err: string
    }
    CheckMint: CheckEventReturnType
    CheckBurn: CheckEventReturnType
    GetAccountList: {
      accounts: {
        accountId: string
        balance: number | string
        accountStatus: string
        reasonCode: number | string
      }[]
      totalCount: number
      err: string
    }
    GetAccount: {
      zoneName: string
      accountName: string
      balance: number
      accountStatus: string
      reasonCode: number | string
      err: string
    }
    HasAccount: CheckEventReturnType
    GetIssuersAll: {
      issuers: Array<IssuerAll>
      totalCount: number
      err: string
    }
  }
  Account: {
    HasAccount: CheckEventReturnType
    IsTerminated: {
      terminated: boolean
      err: string
    }
    BalanceOf: {
      balance: number
      err: string
    }
    GetAccountList: {
      accountListData: StructType['AccountDataType'][]
      totalCount: number
      err: string
    }
    GetAccountId: {
      accountId: string
      err: string
    }
    GetDestinationAccount: {
      accountName: string
      err: string
    }
    GetAccount: {
      accountData: StructType['AccountDataType']
      err: string
    }
    GetAccountCount: number
    GetAccountsAll: {
      accounts: {
        accountId: string
        accountName: string
        accountStatus: string
        zoneIds: Array<number>
        balance: number | string
        reasonCode: number | string
        appliedAt: number | string
        registeredAt: number | string
        terminatingAt: number | string
        terminatedAt: number | string
        accountIdExistence: boolean
        accountEoa: string
        accountApprovalAll: AccountApprovalAll[]
      }[]
      totalCount: number
      err: string
    }
    GetValidatorIdByAccountId: {
      validatorId: string
      err: string
    }
    EmitAfterBalance: void
  }
  BusinessZoneAccount: {
    HasAccountByZone: {
      success: boolean
      err: string
    }
    GetBusinessZoneAccount: {
      businessZoneAccountData: BusinessZoneAccountData
    }
    IsActivatedByZone: {
      success: boolean
      err: string
    }
    AccountIdExistenceByZoneId: {
      success: boolean
    }
    ForceBurnAllBalance: {
      burnedAmount: number
      err: string
    }
    BalanceUpdateByForceBurn: {
      burnedAmount: number
      err: string
    }
    GetZoneIds: number[]
    GetBizAccountsAll: {
      bizAccounts: BizAccountsAllType[]
      totalCount: number
      err: string
    }
  }
  FinancialZoneAccount: {
    GetFinAccountsAll: {
      finAccounts: {
        accountId: string
        financialZoneAccountData: {
          mintLimit: number | string
          burnLimit: number | string
          chargeLimit: number | string
          dischargeLimit: number | string
          transferLimit: number | string
          cumulativeLimit: number | string
          cumulativeAmount: number | string
          cumulativeDate: number | string
          cumulativeTransactionLimits: {
            cumulativeMintLimit: number | string
            cumulativeMintAmount: number | string
            cumulativeBurnLimit: number | string
            cumulativeBurnAmount: number | string
            cumulativeChargeLimit: number | string
            cumulativeChargeAmount: number | string
            cumulativeDischargeLimit: number | string
            cumulativeDischargeAmount: number | string
            cumulativeTransferLimit: number | string
            cumulativeTransferAmount: number | string
          }
        }
      }[]
      totalCount: number
      err: string
    }
    HasAccount: {
      success: boolean
      err: string
    }
    CheckTransfer: {
      success: boolean
      err: string
    }
  }
  Token: {
    GetTotalSupply: {
      totalSupply: number
      err: string
    }
    GetAllowance: {
      allowance: number
      err: string
    }
    GetAllowanceList: {
      approvalData: AccountApprovalAll[]
      totalCount: number
      err: string
    }
    HasToken: CheckEventReturnType
    GetToken: {
      tokenId: string
      name: string
      symbol: string
      totalSupply: number
      enabled: boolean
      err: string
    }
    IsDeposited: boolean
    GetBalanceList: {
      zoneIds: number[]
      zoneNames: string[]
      balances: number[]
      accountNames: string[]
      accountStatus: string[]
      totalBalance: number
      err: string
    }
  }
  IBCToken: {
    CheckAdminRole: {
      has: boolean
      err: string
    }
  }
  FinancialCheck: {
    CheckTransaction: {
      success: boolean
      err: string
    }
    CheckExchange: {
      success: boolean
      err: string
    }
    CheckApprove: {
      success: boolean
      err: string
    }
    CheckSyncAccount: {
      success: boolean
      err: string
    }
    CheckFinAccountStatus: {
      accountStatus: string
      err: string
    }
    GetAccountLimit: {
      accountLimitData: {
        mintLimit?: number | string
        burnLimit?: number | string
        chargeLimit?: number | string
        dischargeLimit?: number | string
        transferLimit?: number | string
        cumulativeLimit?: number | string
        cumulativeAmount?: number | string
        cumulativeAt?: number | string
        cumulativeTransactionLimits: {
          cumulativeMintLimit?: number | string
          cumulativeMintAmount?: number | string
          cumulativeBurnLimit?: number | string
          cumulativeBurnAmount?: number | string
          cumulativeChargeLimit?: number | string
          cumulativeChargeAmount?: number | string
          cumulativeDischargeLimit?: number | string
          cumulativeDischargeAmount?: number | string
          cumulativeTransferLimit?: number | string
          cumulativeTransferAmount?: number | string
        }
      }
      err: string
    }
    GetBizZoneAccountStatus: {
      accountStatus: string
    }
    GetIssuerWithZone: {
      issuers: {
        issuerId: string
        name: string
        bankCode: string
      }[]
      totalCount: number
      err: string
    }
  }
  RenewableEnergyToken: {
    CheckTransaction: {
      success: boolean
      err: string
    }
    GetToken: {
      renewableEnergyTokenData: StructType['RenewableEnergyTokenDataType']
      err: string
    }
    GetTokenList: {
      renewableEnergyTokenList: StructType['RenewableEnergyTokenDataType'][]
      totalCount: number
      err: string
    }

    GetTokenCount: number
    BackupRenewableEnergyTokens: {
      renewableEnergyTokenAll: RenewableEnergyTokenAll[]
      totalCount: number
      err: string
    }
    BackupTokenIdsByAccountIds: {
      tokenIdsByAccountIdAll: TokenIdsByAccountIdAll[]
      totalCount: number
      err: string
    }
  }
  AccessCtrl: {
    CalcRole: string
    CheckAdminRole: {
      has: boolean
      err: string
    }
    CheckRole: EventReturnType['AccessCtrl']['CheckAdminRole']
    HasRole: boolean
  }
  Oracle: {
    GetBatch: {
      values: OracleEventReturnType['GetBatch'][0]
      err: OracleEventReturnType['GetBatch'][1]
    }
    Get: {
      value: OracleEventReturnType['Get'][0]
      err: OracleEventReturnType['Get'][1]
    }
  }
  Remigration: {
    BackupValidators: {
      validators: ValidatorAllType[]
      totalCount: number
      err: string
    }
    BackupProvider: {
      providers: ProviderAll[]
      totalCount: number
      err: string
    }
    BackupAccounts: {
      accounts: AccountAll[]
      totalCount: number
      err: string
    }
    BackupFinAccounts: {
      financialZoneAccounts: FinAccountsAll[]
      totalCount: number
      err: string
    }
    BackupIssuers: {
      issuers: IssuerAll[]
      totalCount: number
      err: string
    }
    BackupToken: {
      token: TokenAll
      err: string
    }
    BackupBusinessZoneAccounts: {
      bizAccountsAll: BizAccountsAll[]
      totalCount: number
      err: string
    }
  }
}

// Solidityのenumと合わせているが、なぜか文字列型で返却されるので、文字列型で定義
export enum TokenStatus {
  Empty = '0', // トークンがまだ存在しない
  Active = '1', // トークンがアクティブで取引可能
  Inactive = '2', // トークンが一時的に非アクティブ
  Cancelled = '3', // トークンがキャンセルまたは無効化
  Frozen = '4', // トークンが凍結されている
  Pending = '5', // トークンが保留中（承認待ちなど）
  Minted = '6', // トークンが新規に発行された
  Burned = '7', // トークンが焼却され、市場から削除された
}

export type StructType = {
  AccountDataType: {
    accountName: string
    balance: number
    accountStatus: string
    reasonCode: number | string
    appliedAt: number
    registeredAt: number
    terminatingAt: number
    terminatedAt: number
    mintLimit: number
    burnLimit: number
    chargeLimit: number
    transferLimit: number
    cumulativeLimit: number
    cumulativeAmount: number
    cumulativeDate: number
    cumulativeTransactionLimits: CumulativeTransactionLimits
  }

  RenewableEnergyTokenDataType: {
    tokenStatus: TokenStatus
    metadataId: string
    metadataHash: string
    mintAccountId: string
    ownerAccountId: string
    previousAccountId: string
    isLocked: boolean
  }
}

export type CumulativeTransactionLimits = {
  cumulativeMintLimit: number | string
  cumulativeMintAmount: number | string
  cumulativeBurnLimit: number | string
  cumulativeBurnAmount: number | string
  cumulativeChargeLimit: number | string
  cumulativeChargeAmount: number | string
  cumulativeDischargeLimit: number | string
  cumulativeDischargeAmount: number | string
  cumulativeTransferLimit: number | string
  cumulativeTransferAmount: number | string
}

export type EventParamOptionType = {
  AccessCtrl: {
    CheckAdminRoleOption: { hash: string }
    CheckRoleOption: EventParamOptionType['AccessCtrl']['CheckAdminRoleOption']
  }
}

export type ContractCallOption = {
  sig?: ReturnType<typeof privateKey.sig>
  deadline?: number
  eoaKey: number
  privateKeyForSig: string
  hash?: string
  validatorId?: string
}

// TODO: 下記に定義してあるイベント呼び出し時のOptionの型をEventParamOptionTypeに移行する
// Provider Option
type ProviderCallOption = {
  providerId: string
}
export type AddProviderOption = {
  zoneId: number
  zoneName: string
} & ProviderCallOption
export type AddProviderRoleOption = {
  providerEoa: string
} & ProviderCallOption
type TokenOption = {
  tokenId: string
  name: string
  symbol: string
}
export type ModZoneOption = {
  providerId: string
  zoneName: string
  traceId: string
}
export type AddTokenOption = TokenOption & ProviderCallOption
export type ModTokenOption = TokenOption & Omit<ProviderCallOption, 'providerId'>
export type GetProviderAllOption = {
  providerId: string
  providerData: ProviderData
  providerEoa: string
  zoneData: ZoneData[]
} & ProviderCallOption
export type SetProviderAllOption = {
  hash: string
}
export type RestoreProviderOption = {
  hash: string
}
export type RestoreAccountsOption = {
  hash: string
}
export type RestoreFinAccountsOption = {
  hash: string
}
export type SetEnabledOption = ProviderCallOption
export type ModProviderOption = ProviderCallOption
export type CheckRoleOption = ProviderCallOption

// Issuer Option
type IssuerCallOption = {
  issuerId: string
}
export type IssuerCheckRoleOption = {
  hash: string
} & IssuerCallOption
export type IssuerCheckOption = {
  accountId: string
} & IssuerCallOption
export type AddIssuerOption = {
  bankCode: number
  name: string
} & IssuerCallOption
export type AddBizZoneToIssuerOption = {
  zoneId: number
} & IssuerCallOption
export type AddAccountIdOption = {
  accountId: string
} & IssuerCallOption
export type ModIssuerOption = AddIssuerOption
export type AddIssuerRoleOption = {
  issuerEoa: string
} & IssuerCallOption
export type IssuerSetEnabledOption = IssuerCallOption
export type ModTokenLimitOption = {
  accountId: string
  limitUpdates: AccountLimitUpdates
  limitValues: AccountLimitValues
} & IssuerCallOption
export type CumulativeResetOption = {
  accountId: string
} & IssuerCallOption
export type SetAccountStatusOption = {
  accountId: string
  accountStatus: string
  reasonCode: number | string
} & IssuerCallOption
export type AddAccountRoleOption = {
  accountId: string
  accountEoa: string
} & IssuerCallOption
export type SetAccountEnabledOption = {
  accountId: string
  reasonCode: number | string
} & IssuerCallOption
export type SetAccountIdentifyOption = {
  accountId: string
} & IssuerCallOption
export type ForceBurnOption = {
  accountId: string
} & IssuerCallOption
export type PartialForceBurnOption = {
  accountId: string
  burnedAmount: number
  burnedBalance: number
} & IssuerCallOption
export type GetIssuersAllOption = {
  hash: string
  offset: number
  limit: number
} & IssuerCallOption
export type SetIssuersAllOption = {
  hash: string
  issuers: IssuerAll[]
}

// Validator Option
type ValidatorCallOption = {
  validatorId: string
}
export type ValidatorCheckRoleOption = {
  hash: string
} & ValidatorCallOption
export type AddValidatorOption = { name: string; issuerId: string } & ValidatorCallOption
export type AddValidatorRoleOption = {
  validatorEoa: string
} & ValidatorCallOption
export type SetValidatorEnabledOption = ValidatorCallOption
export type AddAccountOption = { accountId: string; accountName: string; limitValues?: AccountLimitValues } & Pick<
  ValidatorCallOption,
  'validatorId'
>
export type SetActiveBusinessAccountWithZoneOption = { accountId: string; zoneId: number; accountName: string } & Pick<
  ValidatorCallOption,
  'validatorId'
>
export type AddValidatorAccountIdOption = { validatorId: string; accountId: string }
export type SyncAccountOption = {
  accountId: string
  accountName: string
  zoneId: number // TODOコントラクト側で削除したタイミングでUT側も削除
  zoneName: string
  accountStatus: string
  reasonCode: string
  approvalAmount?: number
} & Pick<ValidatorCallOption, 'validatorId'>
export type ModValidatorOption = ValidatorCallOption
export type ModAccountOption = {
  validatorId: string
  accountId: string
  accountName: string
  traceId: string
  deadline: number
  sig: ReturnType<typeof privateKey.sig>
} & Pick<ValidatorCallOption, 'validatorId'>
export type SetTerminatedOption = {
  accountId: string
} & Pick<ValidatorCallOption, 'validatorId'>
export type SetAccountOpenStateOption = {
  accountId: string
  zoneId: number
  stateCode: number
} & Pick<ValidatorCallOption, 'validatorId'>
export type GetValidatorsAllOption = {
  offset: number
  limit: number
  hash: string
}
export type SetValidatorAllOption = {
  hash: string
}
export type RestoreValidatorsOption = {
  hash: string
}
export type RestoreIssuersOption = {
  hash: string
}
export type RestoreBusinessZoneAccountsOption = {
  hash: string
}
export type RestoreTokenOption = {
  hash: string
}

// AccountOption
export type GetAccountsAllOption = {
  offset: number
  limit: number
  hash: string
}

// FinancialAccountOption
export type GetFinAccountsAllOption = {
  offset: number
  limit: number
  hash: string
}

// TokenOption
export type CheckAdminRoleOption = {
  tokenId: string
}
export type HasValidatorRoleOption = ValidatorCallOption
export type SetTokenEnabledOption = {
  providerId: string
  tokenId: string
}
export type MintOption = {
  issuerId: string
  accountId: string
  from: SignerWithAddress
}
export type ApproveOption = {
  validatorId: string
  ownerId: string
  from: SignerWithAddress
}
export type BurnOption = {
  issuerId: string
  accountId: string
}
export type BurnCancelOption = BurnOption
export type TransferSingleOption = {
  sendAccountId: string
  fromAccountId: string
  toAccountId: string
  zoneId: number
}

// FinancialCheckOption
export type FinancialCheckCallOption = {
  validatorId: string
  accountId: string
}

// BusinessZoneAccountOption
type BusinessZoneCallOption = {
  accountId: string
  zoneId: number
  accountName: string
}
export type SyncBusinessZoneBalanceOption = {
  balance: number
} & BusinessZoneCallOption
export type SyncBusinessZoneStatusOption = {
  accountStatus: string
  zoneName: string
} & BusinessZoneCallOption
export type BalanceUpdateByRedeemVoucherOption = {
  amount: number
} & Pick<BusinessZoneCallOption, 'zoneId' | 'accountId'>
export type BalanceUpdateByIssueVoucherOption = {
  amount: number
} & Pick<BusinessZoneCallOption, 'zoneId' | 'accountId'>
export type GetBizAccountsAllOption = {
  offset: number
  limit: number
  hash: string
}
export type SetBizAccountsAllOption = {
  hash: string
  bizAccounts: BizAccountsAllType[]
}

// RenewableEnergyContractOption
export type MintRenewableEnergyTokenOption = {
  metadataId: string
  metadataHash: string
  mintAccountId: string
  ownerAccountId: string
  isLocked: boolean
}
export type TransferRenewableEnergyTokenOption = {
  fromAccountId: string
  toAccountId: string
  tokenId: string
}
export type CheckTransactionTokenOption = {
  fromAccountId: string
  toAccountId: string
  tokenId: string
  miscValue1: string
  miscValue2: string
}
export type GetTokenListOption = {
  accountId: string
  offset: number
  limit: number
  sortOrder: string
}
export type GetTokenOption = {
  tokenId: string
}

export type PacketCallOption = {
  fromZoneId?: number
  toZoneId?: number
  amount?: number
  ack?: string
  transferType?: string
  packetData?: string | PacketType
}
// AccountSyncBridge
export type SyncAccountCallOption = {
  validatorId?: string
  accountId?: string
  accountName?: string
  fromZoneId?: number
  toZoneId?: number
  zoneName?: string
  accountStatus?: string
  reasonCode?: string
  approvalAmount?: number
  traceId?: string
  timeoutHeight?: number
}
// BalanceSyncBridge
export type SyncTransferCallOption = {
  fromAccountId?: string
  fromAccountName?: string
  toAccountId?: string
  toAccountName?: string
  fromZoneId?: number
  toZoneId?: number
  amount?: number
  miscValue1?: string
  miscValue2?: string
  timeoutHeight?: number
  memo?: string
  traceId?: string
}
// JPYTokenTransferBridge
export type TransferCallOption = {
  accountId?: string
  fromZoneId?: number
  toZoneId?: number
  amount?: number
  timeoutHeight?: number
  traceId?: string
}
type CustomContractInstanceType = { address: string }
export type PacketType = {
  packet: {
    sequence?: BigNumberish
    source_port?: string
    source_channel?: string
    destination_port?: string
    destination_channel?: string
    data: BytesLike
    timeout_height?: {
      revision_number: BigNumberish
      revision_height: BigNumberish
    }
    timeout_timestamp?: BigNumberish
  }
  proof?: BytesLike
  proofHeight?: {
    revision_number: BigNumberish
    revision_height: BigNumberish
  }
  nextSequenceRecv?: BigNumberish
}
export type CumulativeLimitValues = {
  total: number
  mint: number
  burn: number
  charge: number
  discharge: number
  transfer: number
}

export type AccountLimitValues = {
  mint: number
  burn: number
  charge: number
  discharge: number
  transfer: number
  cumulative: CumulativeLimitValues
}

export type AccountLimitUpdates = {
  mint: boolean
  burn: boolean
  charge: boolean
  discharge: boolean
  transfer: boolean
  cumulative: CumulativeLimitUpdates
}

export type CumulativeLimitUpdates = {
  total: boolean
  mint: boolean
  burn: boolean
  charge: boolean
  discharge: boolean
  transfer: boolean
}

export type IBCHandlerInstance = IBCHandlerMock
export type JPYTokenTransferBridgeInstance = JPYTokenTransferBridge
export type AccountSyncBridgeInstance = AccountSyncBridge
export type BalanceSyncBridgeInstance = BalanceSyncBridge

export type TransferFromEscrowOption = TransferSingleOption
export type TransferToEscrowOption = Omit<TransferFromEscrowOption, 'sendAccountId'>
export type CustomTransferOption = TransferFromEscrowOption
export type RedeemVoucherOption = { accountId: string }
export type IssueVoucherOption = RedeemVoucherOption

export type AccountInstance = Account
export type FinancialZoneAccountInstance = FinancialZoneAccount
export type BusinessZoneAccountInstance = BusinessZoneAccount
export type IssuerInstance = IssuerLogic
export type IssuerStorageInstance = IssuerStorage
export type ProviderInstance = Provider
export type ProviderStorageInstance = ProviderStorage
export type TokenInstance = Token
export type TokenStorageInstance = TokenStorage
export type IBCTokenInstance = IBCToken
export type RenewableEnergyTokenInstance = RenewableEnergyToken
export type FinancialCheckInstance = FinancialCheck
export type ValidatorInstance = Validator
export type ValidatorStorageInstance = ValidatorStorage
export type TransferProxyInstance = TransferProxy
export type ContractManagerInstance = ContractManager
export type AccessCtrlInstance = AccessCtrl
export type TransferableMock1Instance = TransferableMock1
export type TransferableMock2Instance = TransferableMock2
export type TransferableMock3Instance = TransferableMock3
export type OracleInstance = Oracle
export type IBCTokenMockInstance = IBCTokenMock
export type ProviderMockInstance = ProviderMock
export type ValidatorMockInstance = ValidatorMock
export type AccountMockInstance = AccountMock
export type AccessCtrlMockInstance = AccessCtrlMock
export type BusinessZoneAccountMockInstance = BusinessZoneAccountMock
export type RemigrationRestoreInstance = RemigrationRestore
export type RemigrationBackupInstance = RemigrationBackup
export type StringUtilsMockInstance = StringUtilsMock
