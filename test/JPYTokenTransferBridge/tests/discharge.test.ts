import '@nomicfoundation/hardhat-chai-matchers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { JPYTokenTransferBridgeInstance } from '@test/common/types'
import { jpyTokenTransferBridgeFuncs } from '@test/JPYTokenTransferBridge/helpers/function'
import { JPYTokenTransferBridgeContractType } from '@test/JPYTokenTransferBridge/helpers/types'
import { expect } from 'chai'

describe('discharge()', () => {
  let jpyTokenTransferBridge: JPYTokenTransferBridgeInstance

  const setupFixture = async () => {
    ;({ jpyTokenTransferBridge } = await contractFixture<JPYTokenTransferBridgeContractType>())
  }

  const setupEscrowAccount = async () => {
    await jpyTokenTransferBridgeFuncs.registerEscrowAccount({
      jpyTokenTransferBridge,
      zoneId: BASE.ZONE.BIZ,
      dstChannelID: BASE.ZONE.FIN,
      escrowAccount: BASE.BRIDGE.ESCROW_ACCOUNT,
    })
  }

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('初期状態', () => {
      it('同一リージョン間のディスチャージの場合、エラーがスローされること (FIN → FIN)', async () => {
        const result = jpyTokenTransferBridge.discharge(
          BASE.BRIDGE.ACCOUNT_A,
          BASE.ZONE.FIN,
          BASE.ZONE.FIN,
          BASE.BRIDGE.AMOUNT,
          BASE.TIMEOUT_HEIGHT,
          BASE.TRACE_ID,
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })

      it('同一リージョン間のディスチャージの場合、エラーがスローされること (BIZ → BIZ)', async () => {
        const result = jpyTokenTransferBridge.discharge(
          BASE.BRIDGE.ACCOUNT_A,
          BASE.ZONE.BIZ,
          BASE.ZONE.BIZ,
          BASE.BRIDGE.AMOUNT,
          BASE.TIMEOUT_HEIGHT,
          BASE.TRACE_ID,
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })

      it('FINからBIZへのディスチャージの場合、エラーがスローされること (FIN → BIZ)', async () => {
        const result = jpyTokenTransferBridge.discharge(
          BASE.BRIDGE.ACCOUNT_A,
          BASE.ZONE.FIN,
          BASE.ZONE.BIZ,
          BASE.BRIDGE.AMOUNT,
          BASE.TIMEOUT_HEIGHT,
          BASE.TRACE_ID,
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
    })
  })

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('EscrowAccountが登録されている状態', () => {
      before(async () => {
        await setupEscrowAccount()
      })

      it('BIZ → FINのディスチャージが正常に実行されること', async () => {
        await expect(
          jpyTokenTransferBridge.discharge(
            BASE.BRIDGE.ACCOUNT_A,
            BASE.ZONE.BIZ,
            BASE.ZONE.FIN,
            BASE.BRIDGE.AMOUNT,
            BASE.TIMEOUT_HEIGHT,
            BASE.TRACE_ID,
          ),
        ).to.not.be.reverted
      })
    })
  })
})
