import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { ROLLED_EOA_KEY } from '@test/AccessCtrl/helpers/constant'
import { contractInitialize } from '@test/AccessCtrl/helpers/contractInitialize'
import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { AccessCtrlContractType } from '@test/AccessCtrl/helpers/types'
import { genRoleByPrefix } from '@test/AccessCtrl/helpers/utils'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import { assertEqualForEachField, getExceededDeadline, toBytes32 } from '@test/common/utils'
import { before } from 'mocha'

describe('checkRole()', () => {
  const providerId = BASE.PROV.PROV0.ID
  const PROVIDER_ROLE_EOA_KEY = 2

  let sender
  let accessCtrl: AccessCtrlInstance
  let accounts: SignerWithAddress[]
  let role: string
  let rolledAccount

  const setupFixture = async () => {
    ;({ accounts } = await contractFixture<AccessCtrlContractType>())
    sender = await accounts[7]
  }

  const initData = async () => {
    ;({ accessCtrl } = await contractInitialize({
      accounts,
      customAddress: { provider: await sender.getAddress() },
    }))
    rolledAccount = await accounts[PROVIDER_ROLE_EOA_KEY]
    role = await genRoleByPrefix({
      accessCtrl,
      getRolePrefixFuncName: 'ROLE_PREFIX_PROV',
      id: providerId,
    })
    await accessCtrlFuncs.addRoleByProv({
      accessCtrl,
      accounts,
      providerId,
      role,
      account: await rolledAccount.getAddress(),
      options: {
        sender,
      },
    })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
      await initData()
    })

    describe('アカウントにProvider権限が付与されている状態', () => {
      it('指定した権限がある場合、trueが返されること', async () => {
        const result = await accessCtrlFuncs.checkRole({
          accessCtrl,
          role,
          account: await rolledAccount.getAddress(),
          options: { eoaKey: PROVIDER_ROLE_EOA_KEY },
        })

        assertEqualForEachField(result, { has: true, err: '' })
      })

      it('Provider権限でない署名の場合、falseが返されること', async () => {
        const result = await accessCtrlFuncs.checkRole({
          accessCtrl,
          role,
          account: await rolledAccount.getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.ADMIN },
        })

        assertEqualForEachField(result, { has: false, err: '' })
      })

      it('署名期限切れの場合、falseが返されること', async () => {
        const exceededDeadline = await getExceededDeadline()
        const result = await accessCtrlFuncs.checkRole({
          accessCtrl,
          role,
          account: await rolledAccount.getAddress(),
          options: { eoaKey: PROVIDER_ROLE_EOA_KEY, deadline: exceededDeadline },
        })

        assertEqualForEachField(result, { has: false, err: ERR.ACTRL.ACTRL_SIG_TIMEOUT })
      })

      it('署名が無効の場合、falseが返されること', async () => {
        const result = await accessCtrlFuncs.checkRole({
          accessCtrl,
          role,
          account: await rolledAccount.getAddress(),
          options: { eoaKey: PROVIDER_ROLE_EOA_KEY, sig: ['0x123456', ''] },
        })

        assertEqualForEachField(result, { has: false, err: ERR.ACTRL.ACTRL_BAD_SIG })
      })

      it('署名の長さが65バイト未満の場合、GS0001エラーが返されること', async () => {
        const shortSig = '0x' + '00'.repeat(128)
        const result = await accessCtrlFuncs.checkRole({
          accessCtrl,
          role,
          account: await rolledAccount.getAddress(),
          options: { eoaKey: PROVIDER_ROLE_EOA_KEY, sig: [shortSig.slice(0, -2), ''] },
        })

        assertEqualForEachField(result, { has: false, err: ERR.ACTRL.ACTRL_BAD_SIG })
      })

      it('署名の長さが65バイト超過の場合、GS0001エラーが返されること', async () => {
        const longSig = '0x' + '00'.repeat(132)
        const result = await accessCtrlFuncs.checkRole({
          accessCtrl,
          role,
          account: await rolledAccount.getAddress(),
          options: { eoaKey: PROVIDER_ROLE_EOA_KEY, sig: [longSig, ''] },
        })

        assertEqualForEachField(result, { has: false, err: ERR.ACTRL.ACTRL_BAD_SIG })
      })

      it('チェック対象の権限が空（0）の場合、falseが返されること', async () => {
        const result = await accessCtrlFuncs.checkRole({
          accessCtrl,
          role: await toBytes32(''),
          account: await rolledAccount.getAddress(),
          options: { eoaKey: PROVIDER_ROLE_EOA_KEY },
        })

        assertEqualForEachField(result, { has: false, err: ERR.ACTRL.ACTRL_NOT_ROLE })
      })
    })
  })
})
