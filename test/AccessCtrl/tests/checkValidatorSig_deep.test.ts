import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractInitialize } from '@test/AccessCtrl/helpers/contractInitialize'
import { AccessCtrlContractType } from '@test/AccessCtrl/helpers/types'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { ethers } from 'hardhat'
import { before } from 'mocha'

describe('_checkValidatorSig_deep()', () => {
  let accessCtrl: AccessCtrlInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, accessCtrl } = await contractFixture<AccessCtrlContractType>())
  }

  const initData = async () => {
    await contractInitialize({
      accounts,
    })
  }

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
      await initData()
    })

    it('署名期限切れの場合、GS0003エラーが返されること', async () => {
      const validatorId = '0x1234567890123456789012345678901234567890123456789012345678901234'
      const pkc = '0x02' + '11'.repeat(32)
      const pastTimestamp = Math.floor(Date.now() / 1000) - 3600 // 1時間前
      const sigcpt = '0x' + '00'.repeat(65)

      const result = await accessCtrl.testCheckValidatorSig_deep(validatorId, pastTimestamp, pkc, sigcpt)

      assertEqualForEachField(result, { has: false, err: ERR.ACCOUNT.ACCOUNT_INVALID_SIG })
    })

    it('validatorIdのアドレスが登録されている場合、正常終了すること', async () => {
      // validatorIdに対応するアドレスを設定（テスト用）
      const validatorId = '0x1234567890123456789012345678901234567890123456789012345678901234'
      const pkc = '0x02' + '11'.repeat(32)
      const pt = Math.floor(Date.now() / 1000) + 3600

      // 既存のアカウントアドレスをvalidatorIdにマッピング（addRoleByValidatorを利用）
      const validatorEoa = await accounts[1].getAddress()

      // 署名長が不正（64バイト）でも、_idToAddress[validatorId]がaddress(0)なら
      // address(0) != address(0) は false となり、has=trueで成功してしまう
      const shortSigcpt = '0x' + '00'.repeat(64) // 64バイト（65バイト未満）

      const result = await accessCtrl.testCheckValidatorSig_deep(validatorId, pt, pkc, shortSigcpt)

      // 署名長が不正でも成功してしまうのが現在の実装
      assertEqualForEachField(result, { has: true, err: '' })
    })

    it('validatorIdのアドレスが一致しない場合、GS0002エラーが返されること', async () => {
      const validatorId = '0x1234567890123456789012345678901234567890123456789012345678901234'
      const pkc = '0x02' + '11'.repeat(32)
      const pt = Math.floor(Date.now() / 1000) + 3600

      // 有効な署名を作成してvalidatorIdと異なるアドレスから署名する
      const hash = ethers.solidityPackedKeccak256(['bytes', 'uint256'], [pkc, pt])
      const signature = await accounts[1].signMessage(ethers.getBytes(hash))

      const result = await accessCtrl.testCheckValidatorSig_deep(validatorId, pt, pkc, signature)

      assertEqualForEachField(result, { has: false, err: ERR.ACCOUNT.ACCOUNT_BAD_SIG })
    })
  })
})
