import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { AccessCtrlContractType } from '@test/AccessCtrl/helpers/types'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { ethers } from 'hardhat'
import { before } from 'mocha'

describe('checkSigAccount()', () => {
  let accessCtrl: AccessCtrlInstance
  let accounts: SignerWithAddress[]

  const setupFixture = async () => {
    ;({ accounts, accessCtrl } = await contractFixture<AccessCtrlContractType>())
  }

  describe('準正常系', () => {
    before(async () => {
      await setupFixture()
    })

    it('署名の長さが65バイト未満の場合、GS0001エラーが返されること', async () => {
      const accountId = '0x1234567890123456789012345678901234567890123456789012345678901234'
      const hashAccount = '0x1234567890123456789012345678901234567890123456789012345678901234'
      const accountSignature = '0x' + '00'.repeat(64) // 64バイト（65バイト未満）

      // 正常な長さのinfoを使って、_checkAccountSig_deepで_recoverAddrを呼び出す
      const pko = '0x02' + '11'.repeat(32)
      const pkc = '0x02' + '22'.repeat(32)
      const pt = Math.floor(Date.now() / 1000) + 3600
      const sigcpt = '0x' + '33'.repeat(65) // 正常な長さ
      const info = ethers.AbiCoder.defaultAbiCoder().encode(
        ['bytes', 'bytes', 'uint256', 'bytes'],
        [pko, pkc, pt, sigcpt],
      )

      const result = await accessCtrl.checkSigAccount(accountId, hashAccount, accountSignature, info)

      assertEqualForEachField(result, { has: false, err: ERR.ACCOUNT.ACCOUNT_BAD_SIG })
    })

    it('署名の長さが65バイト超過の場合、GS0001エラーが返されること', async () => {
      const accountId = '0x1234567890123456789012345678901234567890123456789012345678901234'
      const hashAccount = '0x1234567890123456789012345678901234567890123456789012345678901234'
      const accountSignature = '0x' + '00'.repeat(66) // 66バイト（65バイト超過）

      // 正常な長さのinfoを使って、_checkAccountSig_deepで_recoverAddrを呼び出す
      const pko = '0x02' + '11'.repeat(32)
      const pkc = '0x02' + '22'.repeat(32)
      const pt = Math.floor(Date.now() / 1000) + 3600
      const sigcpt = '0x' + '33'.repeat(65) // 正常な長さ
      const info = ethers.AbiCoder.defaultAbiCoder().encode(
        ['bytes', 'bytes', 'uint256', 'bytes'],
        [pko, pkc, pt, sigcpt],
      )

      const result = await accessCtrl.checkSigAccount(accountId, hashAccount, accountSignature, info)

      assertEqualForEachField(result, { has: false, err: ERR.ACCOUNT.ACCOUNT_BAD_SIG })
    })

    it('OneTime公開鍵検証でアドレスが一致しない場合、GS0002エラーが返されること', async () => {
      const accountId = '0x1234567890123456789012345678901234567890123456789012345678901234'
      const hashAccount = '0x1234567890123456789012345678901234567890123456789012345678901234'
      const accountSignature = '0x' + '00'.repeat(65)

      // accountIdに対するアドレスは登録されていない（address(0)）ため、
      // eccAddRecoverで計算されたアドレスとは必ず一致しない
      const pko = '0x02' + '11'.repeat(32)
      const pkc = '0x02' + '22'.repeat(32)
      const pt = Math.floor(Date.now() / 1000) + 3600
      const sigcpt = '0x' + '33'.repeat(65)
      const info = ethers.AbiCoder.defaultAbiCoder().encode(
        ['bytes', 'bytes', 'uint256', 'bytes'],
        [pko, pkc, pt, sigcpt],
      )

      const result = await accessCtrl.checkSigAccount(accountId, hashAccount, accountSignature, info)

      assertEqualForEachField(result, { has: false, err: ERR.ACCOUNT.ACCOUNT_BAD_SIG })
    })

    it('Account署名検証でアドレスが一致しない場合、GS0002エラーが返されること', async () => {
      const accountId = '0x1234567890123456789012345678901234567890123456789012345678901234'
      const hashAccount = '0x1234567890123456789012345678901234567890123456789012345678901234'

      // 署名から復元されるアドレスと公開鍵から復元されるアドレスが異なるように設定
      const accountSignature = '0x' + '11'.repeat(65) // 異なる署名

      const pko = '0x02' + '11'.repeat(32)
      const pkc = '0x02' + '22'.repeat(32)
      const pt = Math.floor(Date.now() / 1000) + 3600
      const sigcpt = '0x' + '33'.repeat(65)
      const info = ethers.AbiCoder.defaultAbiCoder().encode(
        ['bytes', 'bytes', 'uint256', 'bytes'],
        [pko, pkc, pt, sigcpt],
      )

      const result = await accessCtrl.checkSigAccount(accountId, hashAccount, accountSignature, info)

      assertEqualForEachField(result, { has: false, err: ERR.ACCOUNT.ACCOUNT_BAD_SIG })
    })
  })
})
