import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, RemigrationBackupInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { remigrationFuncs } from '@test/RemigrationBackup/helpers/function'
import { RemigrationBackupContractType } from '@test/RemigrationBackup/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

describe('backupToken()', () => {
  let accounts: SignerWithAddress[]
  let validator: ValidatorInstance
  let issuer: IssuerInstance
  let provider: ProviderInstance
  let remigrationBackup: RemigrationBackupInstance

  const setupFixture = async () => {
    ;({ accounts, provider, issuer, validator, remigrationBackup } =
      await contractFixture<RemigrationBackupContractType>())
  }

  const setupTokenData = async () => {
    await providerFuncs.addProvider({
      provider,
      accounts,
      options: {
        zoneId: BASE.ZONE_ID.ID0,
        zoneName: BASE.ZONE_NAME.NAME0,
      },
    })
    await providerFuncs.addProviderRole({ provider, accounts })
    await issuerFuncs.addIssuer({ issuer, accounts })
    await issuerFuncs.addIssuerRole({ issuer, accounts })
    await validatorFuncs.addValidator({ validator, accounts })
    await providerFuncs.addToken({ provider, accounts })
  }

  describe('正常系', () => {
    before(async () => {
      await setupFixture()
    })

    describe('tokenが登録されていない状態', () => {
      const expectResult = {
        tokenId: BASE.TOKEN.EMPTY,
        name: BASE.TOKEN.EMPTY,
        symbol: BASE.TOKEN.EMPTY,
        totalSupply: '0',
        enabled: false,
      }

      it('空のtoken情報が取得できること', async () => {
        const result = await remigrationFuncs.backupToken({ remigration: remigrationBackup })
        await expect(result.token).to.deep.equal(Object.values(expectResult))
        await expect(result.err).to.equal('')
      })
    })

    describe('Tokenが登録されている状態', () => {
      before(async () => {
        await setupTokenData()
      })

      const expectResult = {
        tokenId: BASE.TOKEN.TOKEN1.ID,
        name: BASE.TOKEN.TOKEN1.NAME,
        symbol: BASE.TOKEN.TOKEN1.SYMBOL,
        totalSupply: '0',
        enabled: true,
      }

      it('1件が取得できること', async () => {
        const result = await remigrationFuncs.backupToken({ remigration: remigrationBackup })
        expect(result.token).to.deep.equal(Object.values(expectResult))
        expect(result.err).to.equal('')
      })

      it('Admin権限がない場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const result = await remigrationFuncs.backupIssuers({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
          options: { eoaKey: 9 },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_BAD_ROLE })
      })

      it('署名無効の場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const result = await remigrationFuncs.backupIssuers({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
          options: {
            sig: ['0x1234', ''],
          },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_BAD_SIG })
      })

      it('署名期限切れの場合、エラーが返されること', async () => {
        const offset = 0
        const limit = 20
        const now = await utils.getExceededDeadline()
        const result = await remigrationFuncs.backupIssuers({
          remigration: remigrationBackup,
          offset: offset,
          limit: limit,
          options: { deadline: now },
        })
        utils.assertEqualForEachField(result, { totalCount: 0, err: ERR.ACTRL.ACTRL_SIG_TIMEOUT })
      })
    })
  })

  describe('準正常系 - backupToken()エラーケース', () => {
    before(async () => {
      await setupFixture()
      await setupTokenData()
    })

    it('Admin権限がない場合、RV0001_ACTRL_BAD_ROLE エラーが返されること', async () => {
      const result = await remigrationFuncs.backupToken({
        remigration: remigrationBackup,
        options: { eoaKey: 9 }, // Admin権限のないEOAを使用
      })
      expect(result.err).to.equal(ERR.ACTRL.ACTRL_BAD_ROLE)
    })

    it('署名無効の場合、backupToken()でエラーが返されること', async () => {
      const result = await remigrationFuncs.backupToken({
        remigration: remigrationBackup,
        options: {
          sig: ['0x1234', ''],
        },
      })
      expect(result.err).to.equal(ERR.ACTRL.ACTRL_BAD_SIG)
    })

    it('署名期限切れの場合、backupToken()でエラーが返されること', async () => {
      const now = await utils.getExceededDeadline()
      const result = await remigrationFuncs.backupToken({
        remigration: remigrationBackup,
        options: { deadline: now },
      })
      expect(result.err).to.equal(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
    })
  })
})
