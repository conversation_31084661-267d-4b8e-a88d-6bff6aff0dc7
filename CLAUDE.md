# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

This is a Hardhat-based Solidity project for DCBG DCJPY contract - a blockchain-based Japanese Yen (JPY) token system with cross-zone functionality. The project includes smart contracts, comprehensive testing, deployment automation, and migration tools for production environments.

## Essential Development Commands

### Build and Compilation
- `npm run postinstall` - Compile contracts and generate TypeScript types
- `npx hardhat compile --force` - Force compilation of Solidity contracts
- `npm run generate-types` - Generate TypeScript types from contract ABIs

### Testing
- `npm test` - Run all tests with build and size check
- `npm run test-all` - Run all individual test suites
- `npm run test:account` - Run Account contract tests
- `npm run test:issuer` - Run Issuer contract tests
- `npm run test:token` - Run Token contract tests
- `npm run test:validator` - Run Validator contract tests
- Individual contract tests available for: access-ctrl, contractManager, provider, transfer-proxy, providerMigrate
- `npm run test-local-scenario:main_functions` - Run local scenario tests
- `npm run coverage` - Generate code coverage report

### Linting and Code Quality
- `npm run lint:fix` - Run ESLint with auto-fix
- `npm run solhint` - Run Solidity linter
- `npm run prettier` - Format code with Prettier
- `npm run size` - Check contract sizes and generate report

### Development Scripts (in ./scripts/)
- `scripts/build.sh` - Compile Solidity with --force
- `scripts/test.sh [files]` - Run Hardhat tests (all or specific files)
- `scripts/coverage.sh '{test/file1.ts,test/file2.ts}'` - Run coverage on specific files
- `scripts/format.sh` - Auto-format Solidity files
- `scripts/docs.sh` - Generate API documentation
- `scripts/clean.sh` - Delete build artifacts (use cautiously in production)
- `scripts/lint.sh` - Run solhint linting

## Project Architecture

### Core Smart Contract System
The project implements a multi-zone blockchain system with the following main components:

**Core Contracts:**
- `Account.sol` - User account management and balance tracking
- `Token.sol` - JPY token implementation with mint/burn/transfer functionality
- `Issuer.sol` - Token issuance and management by authorized entities
- `Validator.sol` - Account validation and zone management
- `Provider.sol` - Service provider management across zones
- `AccessCtrl.sol` - Role-based access control system
- `ContractManager.sol` - Central contract registry and management

**Cross-Zone Communication:**
- `AccountSyncBridge.sol` - Account synchronization between zones
- `BalanceSyncBridge.sol` - Balance synchronization across zones
- `JPYTokenTransferBridge.sol` - Cross-zone token transfers
- `IBCToken.sol` - IBC (Inter-Blockchain Communication) token operations

**Zone-Specific Contracts:**
- `BusinessZoneAccount.sol` - Business zone account management
- `FinancialZoneAccount.sol` - Financial zone account management with limits
- `FinancialCheck.sol` - Financial compliance and transaction validation

**Migration and Backup:**
- `RemigrationBackup.sol` / `RemigrationRestore.sol` - Data migration tools

**Renewable Energy:**
- `RenewableEnergyToken.sol` - Renewable energy credit token system
- `Oracle.sol` - External data oracle for renewable energy verification

### Project Structure
- `contracts/` - Solidity smart contracts organized by functionality
- `test/` - Comprehensive test suites mirroring contract structure
- `tasks/` - Hardhat tasks for contract interaction and management
- `scripts/` - Shell scripts for development workflows
- `deploy/` - Deployment scripts for different environments
- `bin/` - Executable scripts and environment configurations
- `docs/` - Auto-generated API documentation and design documents

### Testing Architecture
Tests are organized by contract with dedicated helpers and focused test files:
- Each contract has its own test directory with helpers and individual test files
- `test/common/` - Shared testing utilities and fixtures
- Local scenario tests in `test_local_scenario/` for integration testing
- Performance and stress testing capabilities

### Environment and Configuration
- Uses `envVers.ts` for centralized environment variable management
- Supports multiple networks: local, localFin, localBiz, main
- KMS integration for secure key management in production
- Multi-zone deployment with separate Financial and Business zone configurations

## Development Workflow

### Initial Setup
1. `git submodule update --init --recursive` - Initialize submodules
2. `npm i` - Install dependencies (includes postinstall compilation)

### Contract Development
1. Write/modify contracts in `contracts/`
2. Run `npm run lint:fix` and `npm run prettier` for code formatting
3. Write comprehensive tests in corresponding `test/` directories
4. Run `npm test` to verify all tests pass and contracts are within size limits
5. Generate documentation with `scripts/docs.sh` if modifying public interfaces

### Task Management
- Use Hardhat tasks in `tasks/` directory for contract interaction
- Example: `npx hardhat getAccount --network localFin --account-id 12345`
- Tasks use kebab-case parameters (camelCase in TypeScript files)

### Production Deployment
- Follow documented procedures in Confluence for production deployments
- Use environment-specific configurations in `bin/main/env/`
- Backup and migration procedures available via `RemigrationBackup/Restore` contracts

## Important Notes

- Node.js version requirement: v18.17.1 (specified in package.json engines)
- Solidity version: 0.8.12 with optimizer enabled
- Contract size monitoring is critical - contracts have deployment size limits
- Auto-linter runs via husky post-commit hooks
- Use `git commit --amend` workflow to include linter changes
- Multi-zone architecture requires careful consideration of cross-zone operations
- KMS integration is used for production key management
- Comprehensive backup/restore system for production data migration