import { Network } from '../helpers/constants'
import { GetAccountAllTask } from '../tasks/GetAccountAllTask'
import { GetAccountInfoTask } from '../tasks/GetAccountInfoTask'

export async function showAccountsStatus(accountId: string) {
  console.info(`show status of FIN Zone accounts: ${accountId}`)
  const localFinTask = new GetAccountAllTask(Network.LocalFin)
  const localFinOuput = await localFinTask.execute({
    accountId,
  })
  console.log(`show status of BIZ Zone accounts: ${accountId}`)
  const localBizTask = new GetAccountAllTask(Network.LocalBiz)
  const localBizOuput = await localBizTask.execute({
    accountId,
  })
  return localFinOuput + localBizOuput
}

export function extractTotalSupply(inputString) {
  const result = {
    totalSupply: 0,
  }

  // Extract totalSupply
  const totalSupplyMatch = inputString.match(/totalSupply\s*\|\s*(\d+)/)

  if (totalSupplyMatch) {
    result.totalSupply = parseInt(totalSupplyMatch[1], 10)
  }

  return result
}

export function extractBurnedAmount(inputString) {
  const result = {
    burnedAmount: 0,
  }

  // Extract burnedAmount
  const matches = [...inputString.matchAll(/burnedAmount\s*\|\s*(\d+)/g)]

  if (matches.length > 0) {
    const last = matches[matches.length - 1][1]
    result.burnedAmount = parseInt(last, 10)
  }

  return result
}

export function extractForceDischargeAmount(inputString: string) {
  const result = {
    forceDischarge: 0,
  }

  // Extract forceDischarge
  const matches = [...inputString.matchAll(/forceDischarge\s*\|\s*[\d]+\s*,\s*(\d+)/g)]

  if (matches.length > 0) {
    const last = matches[matches.length - 1][1]
    result.forceDischarge = parseInt(last, 10)
  }

  return result
}

export function extractAccountInfo(inputString) {
  const result = {
    finZone: {
      status: null,
      balance: null,
      bizZoneAccountStatus: null,
      bizZoneAccountBalance: null,
    },
    bizZone: { status: null, balance: null },
  }

  // Extract accountStatus and balance for finZone (main account)
  const finStatusMatch = inputString.match(/accountStatus\s*\|\s*([^\s|]+)/)
  const finBalanceMatch = inputString.match(/balance\s*\|\s*(\d+)/)

  if (finStatusMatch) {
    result.finZone.status = finStatusMatch[1]
  }
  if (finBalanceMatch) {
    result.finZone.balance = finBalanceMatch[1]
  }

  // Check if Business Zone is linked
  const bizZoneLinked = inputString.includes('Business Zone Account Linkage Status: Linked')

  if (bizZoneLinked) {
    // Extract accountStatus and balance for bizZone (Linked Business Zone Account)
    const bizStatusMatch = inputString.match(
      /--- Linked Business Zone Account ---[\s\S]*?accountStatus\s*\|\s*([^\s|]+)/,
    )
    const bizBalanceMatch = inputString.match(/--- Linked Business Zone Account ---[\s\S]*?balance\s*\|\s*(\d+)/)

    if (bizStatusMatch) {
      result.finZone.bizZoneAccountStatus = bizStatusMatch[1]
    }
    if (bizBalanceMatch) {
      result.finZone.bizZoneAccountBalance = bizBalanceMatch[1]
    }
  }

  // Check for bizZone data (from BIZ Zone query)
  const bizZoneSectionMatch = inputString.match(
    /Execute function on localBiz network[\s\S]*?accountStatus\s*\|\s*([^\s|]+)/,
  )
  const bizZoneBalanceMatch = inputString.match(/Execute function on localBiz network[\s\S]*?balance\s*\|\s*(\d+)/)

  if (bizZoneSectionMatch) {
    result.bizZone.status = bizZoneSectionMatch[1]
  }
  if (bizZoneBalanceMatch) {
    result.bizZone.balance = bizZoneBalanceMatch[1]
  }

  return {
    finZone: {
      status: result.finZone.status ? result.finZone.status.replace(/\x00/g, '') : null,
      balance: result.finZone.balance ? result.finZone.balance : null,
      bizZoneAccountStatus: result.finZone.bizZoneAccountStatus
        ? result.finZone.bizZoneAccountStatus.replace(/\x00/g, '')
        : null,
      bizZoneAccountBalance: result.finZone.bizZoneAccountBalance ? result.finZone.bizZoneAccountBalance : null,
    },
    bizZone: {
      status: result.bizZone.status ? result.bizZone.status.replace(/\x00/g, '') : null,
      balance: result.bizZone.balance ? result.bizZone.balance : null,
    },
  }
}

export async function checkAccountExist(network: Network, accountId: string, validId = '') {
  const params = validId ? { accountId: accountId, validId: validId } : { accountId: accountId }
  const accountInfo = await new GetAccountInfoTask(network).execute(params)
  if (accountInfo.includes(ERROR_CODE.ACCOUNT_NOT_EXISTS)) return ERROR
  return SUCCESS
}

const DEFAULT_TIMEOUT = 15000
export async function delay(ms: number = DEFAULT_TIMEOUT): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

export const SUCCESS = 'Success'
export const ERROR = 'Error'
export const RESULT_OK = 'result | ok'
export const ERROR_CODE = {
  ACCOUNT_NOT_EXISTS: 'GE0105:not exist',
  ACCOUNT_TOO_LARGE_LIMIT: '6104:too large limit',
  FROM_AND_TO_ACCOUNT_ISSUERS_ARE_DIFFERENT: '6209:from and to account issuers are different',
  EXCEEDED_DAILY_MINT_LIMIT: '11022:exceeded daily mint limit',
  EXCEEDED_DAILY_BURN_LIMIT: '11023:exceeded daily burn limit',
  EXCEEDED_DAILY_TRANSFER_LIMIT: '11026:exceeded daily transfer limit',
  EXCEEDED_DAILY_CHARGE_LIMIT: '11024:exceeded daily charge limit',
  EXCEEDED_DAILY_DISCHARGE_LIMIT: '11025:exceeded daily discharge limit',
  ACCOUNT_BALANCE_NOT_ZERO: '6013:account balance not zero',
}
