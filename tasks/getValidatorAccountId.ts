import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { ValidatorLogic } from '@/types/contracts/ValidatorLogic'

wrappedTask('getValidatorAccountId', 'get ValidatorAccountId', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator Id')
  .setAction(async (taskArguments, hre) => {
    try {
      const validatorId = convertToHex({ hre, value: taskArguments.validatorId || '' })

      console.log(`** getValidatorAccountId Parameters **\n`)
      const params = {
        validatorId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<ValidatorLogic>({ hre, contractName: 'ValidatorLogic' })

      const { accountId, err } = await contract.getValidatorAccountId(validatorId)

      const validatorInfo = {
        accountId,
        error: err,
      }

      console.log(`** ValidatorLogic Account Information **\n`)
      printTable({ data: validatorInfo })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
