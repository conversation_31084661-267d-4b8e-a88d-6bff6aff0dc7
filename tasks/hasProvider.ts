import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { ProviderLogic } from '@/types/contracts/ProviderLogic'

wrappedTask('hasProvider', 'Check if provider exists', { filePath: path.basename(__filename) })
  .addParam('provId', 'provider id')
  .setAction(async (taskArguments, hre) => {
    const providerId = convertToHex({ hre, value: taskArguments.provId || '' })

    console.log(`** hasProvider Parameters **\n`)
    const params = {
      providerId,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner<ProviderLogic>({ hre, contractName: 'ProviderLogic' })

      const { success, err } = await contract.hasProvider(providerId)

      const formattedReceipt = {
        result: success ? 'ok' : 'failed',
        reason: err,
      }

      console.log(`** hasProvider Receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
