import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { getTime, printTable, showErrorDetails } from './common/tools'
import * as PrivateKey from '@/privateKey'
import { IssuerLogic } from '@/types/contracts/IssuerLogic'

wrappedTask('checkBurn', 'Check if an account can burn.', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('amount', 'amount')
  .addParam('issuerKey', 'issuer key')
  .setAction(async (taskArguments, hre) => {
    const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const amount = Number(taskArguments.amount || '')
    const issuerKey = taskArguments.issuerKey || ''

    console.log(`** checkBurn Parameters **\n`)
    const params = {
      issuerId,
      accountId,
      amount,
      issuerKey,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner<IssuerLogic>({ hre, contractName: 'IssuerLogic' })

      const deadline = await getTime()
      const sig = await PrivateKey.sig(
        issuerKey,
        ['bytes32', 'bytes32', 'uint256', 'uint256'],
        [issuerId, accountId, amount, deadline],
      )

      const { success, err } = await contract.checkBurn(issuerId, accountId, amount, deadline, sig[0])

      const formattedReceipt = {
        result: success ? 'ok' : 'failed',
        reason: err,
      }

      console.log(`** checkBurn receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
