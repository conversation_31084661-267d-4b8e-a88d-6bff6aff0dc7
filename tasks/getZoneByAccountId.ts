import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { ValidatorLogic } from '@/types/contracts/ValidatorLogic'
wrappedTask('getZoneByAccountId', 'get Zone By AccountId', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator Id')
  .addParam('accountId', 'account Id')
  .setAction(async (taskArguments, hre) => {
    try {
      const validatorId = convertToHex({ hre, value: taskArguments.validatorId || '' })
      const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })

      console.log(`** getZoneByAccountId Parameters **\n`)
      const params = {
        validatorId,
        accountId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<ValidatorLogic>({ hre, contractName: 'ValidatorLogic' })

      const { zones, err } = await contract.getZoneByAccountId(validatorId, accountId)

      const zoneInfo = {
        zones,
        error: err,
      }

      console.log(`** Zone Information **\n`)
      printTable({ data: zoneInfo })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
