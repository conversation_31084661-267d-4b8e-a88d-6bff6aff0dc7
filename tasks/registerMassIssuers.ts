import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { getTime } from './common/tools'
import { IssuerLogic } from '@/types/contracts/IssuerLogic'

wrappedTask('registerMassIssuers', 'register multiple issuers', {
  filePath: path.basename(__filename),
})
  .addParam('numberOfIssuers', 'number of issuers to register')
  .addParam('issuerId', 'issuer id')
  .addParam('bankCode', 'bank code')
  .addParam('issuerName', 'issuer name')
  .addParam('issuerKey', 'issuer key')
  .setAction(async (taskArguments, hre) => {
    const numberOfIssuers = Number(taskArguments.numberOfIssuers || '1')

    const kmsSigner = kmsSignerProvider({ hre })

    const { bankCode = '', issuerName = '' } = taskArguments
    const issuerId = taskArguments.issuerId || ''
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<IssuerLogic>({ hre, contractName: 'IssuerLogic' })

    const transactionMap = new Map()
    console.log(`*** Start registering ${numberOfIssuers} issuers, this may take a while...`)
    for (let i = 0; i < numberOfIssuers; i++) {
      // issuerId = 300, number = 1000
      // => pad value = 0001, 0002,...
      // => issuerId = 300001, 300002,...
      const padValue = i.toString().padStart(numberOfIssuers.toString().length, '0')
      const issuerIdNumber = `${issuerId}${padValue}`
      const padIssuerId = convertToHex({ hre, value: `${issuerId}${padValue}` })
      const padIssuerName = convertToHex({ hre, value: `${issuerName}${padValue}` })
      const padBankCode = parseInt(bankCode, 10) + i

      const deadline = await getTime()
      const kmsSig = await kmsSigner.sign(
        ['bytes32', 'uint16', 'string', 'uint256'],
        [padIssuerId, padBankCode, padIssuerName, deadline],
      )

      try {
        const tx = await contract.addIssuer(padIssuerId, padBankCode, padIssuerName, traceId, deadline, kmsSig)
        transactionMap.set(issuerIdNumber, tx.hash)
      } catch (e: any) {
        console.error(`Revert at callStatic for issuer ${issuerIdNumber}, bankCode ${padBankCode}`)
        console.error(`Reason: ${e?.error?.message || e?.message || JSON.stringify(e)}`)
        continue
      }

      await new Promise((resolve) => setTimeout(resolve, 2000))
    }

    await new Promise((resolve) => setTimeout(resolve, 20000))

    let registrationSuccessCount = 0
    for (const [issuerIdNumber, hash] of transactionMap) {
      const receipt = await hre.ethers.provider.getTransactionReceipt(hash)
      if (receipt && receipt.status === 1) {
        console.log(`Success to register issuer ${issuerIdNumber}`)
        registrationSuccessCount++
      } else {
        console.log(`Failed to register issuer ${issuerIdNumber}`)
      }
    }

    console.log(`Successfully registered ${registrationSuccessCount} issuers`)
  })
