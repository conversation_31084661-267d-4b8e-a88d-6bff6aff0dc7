import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import * as utils from '@test/common/utils'
import BigNumber from 'bignumber.js'
import { getTime, printTable, showErrorDetails } from './common/tools'
import * as PrivateKey from '@/privateKey'
import { TokenLogic } from '@/types/contracts/TokenLogic'

wrappedTask('checkApprove', 'Check if an account can approve.', { filePath: path.basename(__filename) })
  .addParam('validId', 'validator id')
  .addParam('ownerId', 'owner id')
  .addParam('spenderId', 'spender id')
  .addParam('amount', 'amount')
  .addParam('validKey', 'validator key')
  .setAction(async (taskArguments, hre) => {
    const validatorId = convertToHex({ hre, value: taskArguments.validId || '' })
    const ownerId = convertToHex({ hre, value: taskArguments.ownerId || '' })
    const spenderId = convertToHex({ hre, value: taskArguments.spenderId || '' })
    const amount = Number(taskArguments.amount || '')
    const validatorKey = taskArguments.validKey || ''

    const orderN = '0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141'
    const accountPrivateKey = '0x47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a'
    const issuerPrivateKey = '0x7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6'
    const commitPrivateKey = '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80'
    const accDtn = new BigNumber('999999999999999999')

    const sigInfo = await utils.siginfoGenerator(commitPrivateKey, accountPrivateKey, orderN, issuerPrivateKey, accDtn)
    const msgSalt = utils.toBytes32('approve')
    const accSigInfo = PrivateKey.sig(
      sigInfo.signer,
      ['bytes32', 'bytes32', 'uint256', 'bytes32'],
      [ownerId, spenderId, amount, msgSalt],
    )

    console.log(`** checkApprove Parameters **\n`)
    const params = {
      validatorId,
      ownerId,
      spenderId,
      amount,
      validatorKey,
      sigInfo,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner<TokenLogic>({ hre, contractName: 'TokenLogic' })

      const deadline = await getTime()
      const sig = await PrivateKey.sig(
        validatorKey,
        ['bytes32', 'bytes32', 'uint256'],
        [validatorId, ownerId, deadline],
      )

      const { success, err } = await contract.checkApprove(
        validatorId,
        ownerId,
        spenderId,
        amount,
        accSigInfo[0],
        sigInfo.info,
        deadline,
        sig[0],
      )

      const formattedReceipt = {
        result: success ? 'ok' : 'failed',
        reason: err,
      }

      console.log(`** checkApprove receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
