import fs from 'fs'
import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { getTime, showEthersRes } from './common/tools'
import { ValidatorLogic } from '@/types/contracts/ValidatorLogic'

wrappedTask('performanceMeasurementByRegisterValid', 'performance measurement by registering validator', {
  filePath: path.basename(__filename),
})
  .addParam('validId', 'validator id')
  .addParam('issuerId', 'issuer id')
  .addParam('validatorName', 'validator name')
  .addParam('count', 'count')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const validatorId = taskArguments.validId || ''
    const issuerId = taskArguments.issuerId || ''
    const validatorName = convertToHex({ hre, value: taskArguments.validatorName || '' })
    const count = taskArguments.count || ''
    const traceId = convertToHex({ hre, value: 'trace1' })

    const result: any = []
    const txText: any = []

    const startTime = performance.now()

    const { contract } = await getContractWithSigner<ValidatorLogic>({ hre, contractName: 'ValidatorLogic' })
    contract.connect(kmsSigner)

    const txs = [...Array(count).keys()].map(async (num) => {
      const currentValidatorId = convertToHex({ hre, value: validatorId + num })
      const currentIssuerId = convertToHex({ hre, value: issuerId + num })

      console.log(`*** validatorID登録: ${currentValidatorId}`)

      performance.mark('start' + num)
      const deadline = await getTime()
      const kmsSig = await kmsSigner.sign(
        ['bytes32', 'bytes32', 'bytes32', 'uint256'],
        [currentValidatorId, currentIssuerId, validatorName, deadline],
      )
      const receipt = await contract.addValidator(
        currentValidatorId,
        currentIssuerId,
        validatorName,
        traceId,
        deadline,
        kmsSig,
      )
      await receipt.wait().then((res) => {
        performance.mark('end' + num)
        showEthersRes({ res })
        performance.measure(num.toString(), 'start' + num, 'end' + num)
        performance.getEntriesByName(num.toString())
        result.push(performance.getEntriesByName(num.toString())[0])
        txText.push(res)
      })
    })
    await Promise.all(txs)
      .then(() => console.log('promise-complete'))
      .catch((err) => console.log(err))
    const endTime = performance.now()
    fs.writeFileSync('data-validator-' + validatorId + '.json', JSON.stringify(result))
    fs.writeFileSync('txs-validator-' + validatorId + '.json', JSON.stringify(txText))
    console.log(endTime - startTime)
  })
