import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { getTime, printTable, showErrorDetails } from './common/tools'
import * as PrivateKey from '@/privateKey'
import { IssuerLogic } from '@/types/contracts/IssuerLogic'

wrappedTask('addAccountRole', 'Add account role', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account id')
  .addParam('accountKey', 'account key')
  .addParam('issuerKey', 'issuer key')
  .addParam('issuerId', 'issuer id')
  .setAction(async (taskArguments, hre) => {
    try {
      const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
      const accountKey = taskArguments.accountKey || ''
      const issuerKey = taskArguments.issuerKey || ''
      const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
      const traceId = convertToHex({ hre, value: 'trace1' })

      const addrAccount = new hre.ethers.Wallet(accountKey).address

      console.log(`** addAccountRole Parameters **\n`)
      const params = {
        accountId,
        issuerId,
        accountAddress: addrAccount,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<IssuerLogic>({ hre, contractName: 'IssuerLogic' })

      const deadline = await getTime()
      const sig = await PrivateKey.sig(
        issuerKey,
        ['bytes32', 'bytes32', 'address', 'uint256'],
        [issuerId, accountId, addrAccount, deadline],
      )

      await executeReceipt(contract.addAccountRole(issuerId, accountId, addrAccount, traceId, deadline, sig[0]))
    } catch (error) {
      showErrorDetails({ error })
    }
  })
