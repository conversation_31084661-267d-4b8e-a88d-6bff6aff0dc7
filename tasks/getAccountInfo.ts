import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable } from './common/tools'

wrappedTask('getAccountInfo', 'Gets the Information for account ID.', {
  filePath: path.basename(__filename),
})
  .addParam('accountId', 'account id')
  .addParam('validId', 'validator id')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre

    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const validatorId = convertToHex({ hre, value: taskArguments.validId || '' })

    const { contract } = await getContractWithSigner({ hre, contractName: 'ValidatorLogic' })
    const receipt = (await contract.getAccount(validatorId, accountId))[0]

    const accountInfo = {
      accountId: ethers.toUtf8String(accountId),
      accountName: ethers.toUtf8String(receipt.accountName.toString()),
      accountStatus: ethers.toUtf8String(receipt.accountStatus.toString()),
      balance: receipt.balance.toString(),
      appliedAt: receipt.appliedAt.toString(),
      registeredAt: receipt.registeredAt.toString(),
      terminatingAt: receipt.terminatingAt.toString(),
      terminatedAt: receipt.terminatedAt.toString(),
      mintLimit: receipt.mintLimit.toString(),
      burnLimit: receipt.burnLimit.toString(),
      chargeLimit: receipt.chargeLimit.toString(),
      dischargeLimit: receipt.dischargeLimit.toString(),
      transferLimit: receipt.transferLimit.toString(),
      cumulativeLimit: receipt.cumulativeLimit.toString(),
      cumulativeAmount: receipt.cumulativeAmount.toString(),
      cumulativeDate: receipt.cumulativeDate.toString(),
      cumulativeMintLimit: receipt.cumulativeTransactionLimits.cumulativeMintLimit.toString(),
      cumulativeMintAmount: receipt.cumulativeTransactionLimits.cumulativeMintAmount.toString(),
      cumulativeBurnLimit: receipt.cumulativeTransactionLimits.cumulativeBurnLimit.toString(),
      cumulativeBurnAmount: receipt.cumulativeTransactionLimits.cumulativeBurnAmount.toString(),
      cumulativeChargeLimit: receipt.cumulativeTransactionLimits.cumulativeChargeLimit.toString(),
      cumulativeChargeAmount: receipt.cumulativeTransactionLimits.cumulativeChargeAmount.toString(),
      cumulativeDischargeLimit: receipt.cumulativeTransactionLimits.cumulativeDischargeLimit.toString(),
      cumulativeDischargeAmount: receipt.cumulativeTransactionLimits.cumulativeDischargeAmount.toString(),
      cumulativeTransferLimit: receipt.cumulativeTransactionLimits.cumulativeTransferLimit.toString(),
      cumulativeTransferAmount: receipt.cumulativeTransactionLimits.cumulativeTransferAmount.toString(),
    }

    console.log(`*** Account Information: ${ethers.toUtf8String(accountId)}`)
    printTable({ data: accountInfo })
  })
