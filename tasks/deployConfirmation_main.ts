import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { Contract } from 'ethers'
import { AccessCtrl } from '@/types/contracts/AccessCtrl'
import { Account } from '@/types/contracts/Account'
import { ContractManager } from '@/types/contracts/ContractManager'
import { IssuerLogic } from '@/types/contracts/IssuerLogic'
import { ProviderLogic } from '@/types/contracts/ProviderLogic'
import { TokenLogic } from '@/types/contracts/TokenLogic'
import { TransferProxy } from '@/types/contracts/TransferProxy'
import { ValidatorLogic } from '@/types/contracts/ValidatorLogic'

type ContractType = {
  AccessCtrl: AccessCtrl
  Account: Account
  ContractManager: ContractManager
  Error: Contract
  IssuerLogic: IssuerLogic
  ProviderLogic: ProviderLogic
  TokenLogic: TokenLogic
  TransferProxy: TransferProxy
  ValidatorLogic: ValidatorLogic
}

const TARGET: (keyof ContractType)[] = [
  'AccessCtrl',
  'Account',
  'ContractManager',
  'Error',
  'IssuerLogic',
  'ProviderLogic',
  'TokenLogic',
  'TransferProxy',
  'ValidatorLogic',
] as const

wrappedTask('deployConfirmation_main', 'Prints contracts setted to ContractManager', {
  filePath: path.basename(__filename),
}).setAction(async (_, hre) => {
  for (let lp = 0; lp < TARGET.length; lp++) {
    const { deployed, contract } = await getContractWithSigner<ContractType[keyof ContractType]>({
      hre,
      contractName: TARGET[lp],
    })

    // Errorはversion()が無いため、アドレスのみ表示
    if (TARGET[lp] == 'Error') {
      console.log(`${TARGET[lp]}: ${deployed.address}`)
      continue
    }
    const version = await contract.version()
    console.log(`${TARGET[lp]}: \"${version}\" (${deployed.address})`)
  }
})
