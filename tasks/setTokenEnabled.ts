import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { getTime, printTable, showErrorDetails } from './common/tools'
import * as PrivateKey from '@/privateKey'
import { TokenLogic } from '@/types/contracts/TokenLogic'

wrappedTask('setTokenEnabled', 'set token enabled status', {
  filePath: path.basename(__filename),
})
  .addParam('provKey', 'provider key')
  .addParam('provId', 'provider id')
  .addParam('tokenId', 'token id')
  .addParam('enabled', 'token enabled status (true/false)')
  .setAction(async (taskArguments, hre) => {
    try {
      const providerKey = taskArguments.provKey
      const providerId = convertToHex({ hre, value: taskArguments.provId || '' })
      const tokenId = convertToHex({ hre, value: taskArguments.tokenId || '' })
      const enabled = taskArguments.enabled === 'true' || taskArguments.enabled === true
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** setTokenEnabled Parameters **\n`)
      const params = {
        providerId,
        tokenId,
        enabled,
        providerKey: providerKey ? '***' : 'not provided',
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<TokenLogic>({ hre, contractName: 'TokenLogic' })

      console.log(`*** Set Token Enabled Status: ${enabled ? 'ENABLED' : 'DISABLED'}`)
      const deadline = getTime()
      const sig = PrivateKey.sig(
        providerKey,
        ['bytes32', 'bytes32', 'bool', 'uint256'],
        [providerId, tokenId, enabled, deadline],
      )

      await executeReceipt(contract.setTokenEnabled(providerId, tokenId, enabled, traceId, deadline, sig[0]))
    } catch (error) {
      showErrorDetails({ error })
    }
  })
