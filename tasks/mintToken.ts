import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { TokenLogic } from '@/types/contracts/TokenLogic'

wrappedTask('mintToken', 'mint token', {
  filePath: path.basename(__filename),
})
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('amount', 'mint amount')
  .addParam('issuerKey', 'issuer key')
  .setAction(async (taskArguments, hre) => {
    const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const amount = taskArguments.amount || ''
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<TokenLogic>({ hre, contractName: 'TokenLogic' })

    console.log(`*** Token mint: ${amount}`)

    await executeReceipt(contract.mint(issuerId, accountId, amount, traceId))
  })
