import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { TokenLogic } from '@/types/contracts/TokenLogic'

wrappedTask('getAllowance', 'get Allowance', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator Id')
  .addParam('ownerId', 'owner Id')
  .addParam('spenderId', 'spender Id')
  .setAction(async (taskArguments, hre) => {
    try {
      const validatorId = convertToHex({ hre, value: taskArguments.validatorId || '' })
      const ownerId = convertToHex({ hre, value: taskArguments.ownerId || '' })
      const spenderId = convertToHex({ hre, value: taskArguments.spenderId || '' })

      console.log(`** getAllowance Parameters **\n`)
      const params = {
        validatorId,
        ownerId,
        spenderId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<TokenLogic>({ hre, contractName: 'TokenLogic' })

      const receipt = await contract.getAllowance(validatorId, ownerId, spenderId)

      const formattedReceipt = {
        allowance: receipt.allowance.toString(),
        approvedAt: receipt.approvedAt.toString(),
        error: receipt.err,
      }

      console.log(`** getAllowance Receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
