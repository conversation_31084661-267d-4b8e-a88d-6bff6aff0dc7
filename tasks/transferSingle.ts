import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { executeReceipt } from './common/executeReceipt'
import { wrappedTask } from '@/tasks/common/taskMiddleware'
import { TokenLogic } from '@/types/contracts/TokenLogic'

wrappedTask('transferSingle', 'Execute transfer within the same zone.', {
  filePath: path.basename(__filename),
})
  .addParam('sendAccountId', 'send account id')
  .addParam('fromAccountId', 'from account id')
  .addParam('toAccountId', 'to account id')
  .addParam('amount', 'amount')
  .addParam('miscValue1', 'miscValue1')
  .addParam('miscValue2', 'miscValue2')
  .addParam('memo', 'memo')
  .addParam('traceId', 'traceId')
  .setAction(async (taskArguments, hre) => {
    const sendAccountId = convertToHex({ hre, value: taskArguments.sendAccountId || '' })
    const fromAccountId = convertToHex({ hre, value: taskArguments.fromAccountId || '' })
    const toAccountId = convertToHex({ hre, value: taskArguments.toAccountId || '' })
    const miscValue1 = convertToHex({ hre, value: taskArguments.miscValue1 || '' })
    const miscValue2 = convertToHex({ hre, value: taskArguments.miscValue2 || '' })
    const memo = convertToHex({ hre, value: taskArguments.memo || '' })
    const traceId = convertToHex({ hre, value: taskArguments.traceId || '' })
    const amount = taskArguments.amount || ''

    const { contract } = await getContractWithSigner<TokenLogic>({ hre, contractName: 'TokenLogic' })

    await executeReceipt(
      contract.transferSingle(sendAccountId, fromAccountId, toAccountId, amount, miscValue1, miscValue2, memo, traceId),
    )
  })
