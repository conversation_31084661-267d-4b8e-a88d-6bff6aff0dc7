import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable } from './common/tools'
import { ProviderLogic } from '@/types/contracts/ProviderLogic'

wrappedTask('getProvider', 'get provider', { filePath: path.basename(__filename) }).setAction(async (_, hre) => {
  try {
    const { contract } = await getContractWithSigner<ProviderLogic>({ hre, contractName: 'ProviderLogic' })

    const { providerId, zoneId, zoneName } = await contract.getProvider()

    console.log(`** Provider Information **\n`)
    const formattedProvider = {
      providerId,
      zoneId,
      zoneName,
    }
    printTable({ data: formattedProvider })
  } catch (error) {
    console.error(error)
  }
})
