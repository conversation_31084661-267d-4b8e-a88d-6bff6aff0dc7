import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { getTime, showEthersRes } from './common/tools'
import { ProviderLogic } from '@/types/contracts/ProviderLogic'

wrappedTask('registerProvider', 'register provider', {
  filePath: path.basename(__filename),
})
  .addParam('provId', 'provider id')
  .addParam('zoneId', 'zone id')
  .addParam('zoneName', 'zone name')
  .addParam('provKey', 'provider key')
  .addParam('flag', 'flag')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<ProviderLogic>({ hre, contractName: 'ProviderLogic' })

    contract.connect(kmsSigner)

    const { zoneId = '', zoneName = '', provKey = '', flag = '' } = taskArguments
    const providerId = convertToHex({ hre, value: taskArguments.provId || '' })

    const addrProv = new hre.ethers.Wallet(provKey).address

    if (flag[0] === '1') {
      const provider = await contract.getProvider()
      if (parseInt(provider.providerId) != 0) {
        console.log('*** Providerがすでに登録してあるため登録不可、Prov権限登録（実行する場合）は上書きする')
        console.log('登録済みProvider ID:', provider.providerId)
      } else {
        console.log(`*** provID登録: ${providerId}`)
        const deadline = await getTime()
        const kmsSig = await kmsSigner.sign(['bytes32', 'uint16', 'uint256'], [providerId, zoneId, deadline])

        console.log('providerId', providerId)
        console.log('zoneId', zoneId)
        console.log('zoneName', zoneName)
        const receipt = await contract.addProvider(providerId, zoneId, zoneName, traceId, deadline, kmsSig)

        await receipt
          .wait()
          .then((res) => {
            showEthersRes({ res })
          })
          .catch((error) => console.log(error))
      }
    }

    if (flag[1] === '1') {
      console.log(`*** prov権限登録: ${providerId}=${addrProv}`)

      const deadline = await getTime()
      const kmsSig = await kmsSigner.sign(['bytes32', 'address', 'uint256'], [providerId, addrProv, deadline])

      await executeReceipt(contract.addProviderRole(providerId, addrProv, traceId, deadline, kmsSig))
    }
  })
