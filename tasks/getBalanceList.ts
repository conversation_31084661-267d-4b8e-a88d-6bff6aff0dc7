import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'

wrappedTask('getBalanceList', 'get Balance List', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account id')
  .setAction(async (taskArguments, hre) => {
    try {
      const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })

      console.log(`** getBalanceList Parameters **\n`)
      const params = {
        accountId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner({ hre, contractName: 'TokenLogic' })

      const receipt = await contract.getBalanceList(accountId)

      console.log(JSON.stringify(receipt))

      const formattedReceipt = {
        result: receipt.success ? 'ok' : 'failed',
        error: receipt.err,
      }

      console.log(`** getBalanceList Receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
