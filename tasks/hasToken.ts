import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { TokenLogic } from '@/types/contracts/TokenLogic'

wrappedTask('hasToken', 'has Token', { filePath: path.basename(__filename) })
  .addParam('tokenId', 'token Id')
  .addParam('providerId', 'provider Id')
  .addParam('chkEnabled', 'chk Enabled')
  .setAction(async (taskArguments, hre) => {
    try {
      const tokenId = convertToHex({ hre, value: taskArguments.tokenId || '' })
      const providerId = convertToHex({ hre, value: taskArguments.providerId || '' })
      const chkEnabled = taskArguments.chkEnabled || ''

      console.log(`** hasToken Parameters **\n`)
      const params = {
        tokenId,
        providerId,
        chkEnabled,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<TokenLogic>({ hre, contractName: 'TokenLogic' })

      const receipt = await contract.hasToken(tokenId, chkEnabled)

      console.log(`** hasToken Receipt Information **\n`)
      printTable({ data: receipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
