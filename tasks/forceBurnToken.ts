import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { getTime } from './common/tools'
import * as PrivateKey from '@/privateKey'
import { IssuerLogic } from '@/types/contracts/IssuerLogic'

wrappedTask('forceBurnToken', 'force burn token', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('issuerKey', 'issuer key')
  .setAction(async (taskArguments, hre) => {
    const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const issuerKey = taskArguments.issuerKey || ''
    const traceId = convertToHex({ hre, value: 'traceId' })

    const { contract } = await getContractWithSigner<IssuerLogic>({ hre, contractName: 'IssuerLogic' })

    console.log(`*** Token forceBurn`)
    const deadline = await getTime()
    const sig = await PrivateKey.sig(issuerKey, ['bytes32', 'bytes32', 'uint256'], [issuerId, accountId, deadline])

    await executeReceipt(contract.forceBurn(issuerId, accountId, traceId, deadline, sig[0]))
  })
