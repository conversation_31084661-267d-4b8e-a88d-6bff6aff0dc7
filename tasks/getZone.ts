import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { ProviderLogic } from '@/types/contracts/ProviderLogic'

wrappedTask('getZone', 'get Zone', { filePath: path.basename(__filename) }).setAction(async (_, hre) => {
  try {
    console.log(`** getZone Parameters **\n`)
    console.log('No parameters required')

    const { contract } = await getContractWithSigner<ProviderLogic>({ hre, contractName: 'ProviderLogic' })

    const { zoneId, zoneName, err } = await contract.getZone()

    const zoneInfo = {
      zoneId,
      zoneName,
      error: err,
    }

    console.log(`** Zone Information **\n`)
    printTable({ data: zoneInfo })
  } catch (error) {
    showErrorDetails({ error })
  }
})
