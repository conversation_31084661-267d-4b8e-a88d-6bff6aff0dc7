import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { getTime, printTable, showErrorDetails } from './common/tools'
import * as PrivateKey from '@/privateKey'
import { ValidatorLogic } from '@/types/contracts/ValidatorLogic'

wrappedTask('addValidator', 'add ValidatorLogic', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('validatorId', 'validator Id')
  .addParam('name', 'name')
  .addParam('validatorKey', 'validator Key')
  .setAction(async (taskArguments, hre) => {
    try {
      const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
      const validatorId = convertToHex({ hre, value: taskArguments.validatorId || '' })
      const name = taskArguments.name || ''
      const validatorKey = taskArguments.validatorKey || ''
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** addValidator Parameters **\n`)
      const params = {
        issuerId,
        validatorId,
        name,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<ValidatorLogic>({ hre, contractName: 'ValidatorLogic' })

      const deadline = await getTime()
      const sig = await PrivateKey.sig(
        validatorKey,
        ['bytes32', 'bytes32', 'bytes32', 'uint256'],
        [issuerId, validatorId, name, deadline],
      )

      await executeReceipt(contract.addValidator(issuerId, validatorId, name, traceId, deadline, sig[0]))
    } catch (error) {
      showErrorDetails({ error })
    }
  })
