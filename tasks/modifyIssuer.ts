import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { getTime } from './common/tools'
import { IssuerLogic } from '@/types/contracts/IssuerLogic'

wrappedTask('modifyIssuer', 'register issuer', {
  filePath: path.basename(__filename),
})
  .addParam('issuerId', 'issuer id')
  .addParam('issuerName', 'issuer name')
  .setAction(async (taskArguments, hre) => {
    const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
    const issuerName = taskArguments.issuerName || ''
    const kmsSigner = kmsSignerProvider({ hre })

    const { contract } = await getContractWithSigner<IssuerLogic>({ hre, contractName: 'IssuerLogic' })
    contract.connect(kmsSigner)

    const traceId = convertToHex({ hre, value: 'trace1' })

    console.log(`*** issuerName更新: ${issuerName.toString(16)}`)
    const deadline = await getTime()
    const kmsSig = await kmsSigner.sign(['bytes32', 'string', 'uint256'], [issuerId, issuerName, deadline])

    await executeReceipt(contract.modIssuer(issuerId, issuerName, traceId, deadline, kmsSig))
  })
