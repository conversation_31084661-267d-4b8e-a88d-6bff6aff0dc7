import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { kmsSignerProvider } from './common/kmsSignerProvider'
import { getTime, printTable, showErrorDetails } from './common/tools'
import { ValidatorLogic } from '@/types/contracts/ValidatorLogic'

wrappedTask('modValidator', 'mod ValidatorLogic', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator id')
  .addParam('name', 'name')
  .addParam('validatorKey', 'validator Key')
  .setAction(async (taskArguments, hre) => {
    try {
      const kmsSigner = kmsSignerProvider({ hre })

      const validatorId = convertToHex({ hre, value: taskArguments.validatorId || '' })
      const name = convertToHex({ hre, value: taskArguments.name || '' })
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** modValidator Parameters **\n`)
      const params = {
        validatorId,
        name,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<ValidatorLogic>({ hre, contractName: 'ValidatorLogic' })

      const deadline = await getTime()
      const kmsSig = await kmsSigner.sign(['bytes32', 'bytes32', 'uint256'], [validatorId, name, deadline])

      await executeReceipt(contract.modValidator(validatorId, name, traceId, deadline, kmsSig))
    } catch (error) {
      showErrorDetails({ error })
    }
  })
