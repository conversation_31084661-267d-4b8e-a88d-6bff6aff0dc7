import fs from 'fs'
import path from 'path'
import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { md5Valid } from '@tasks/common/md5Utils'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { showEthersRes } from '@tasks/common/tools'
import { IssuerLogic } from '@/types/contracts/IssuerLogic'
import { RemigrationRestore } from '@/types/contracts/remigration/RemigrationRestore'

wrappedTask('restoreIssuers', 'restore all issuer data', { filePath: path.basename(__filename) }).setAction(
  async (_, hre) => {
    let issuers: Array<any> = []

    const data = fs.readFileSync(`./scripts/backup-restore/backupfiles/${hre.network.name}/issuers.json`, 'utf8')
    const isValid = md5Valid({ obj: data, item: 'issuers', network: hre.network.name })

    if (!isValid) {
      throw new Error('The data is invalid backup data.')
    }

    issuers = JSON.parse(data)

    await getContractWithSigner<IssuerLogic>({
      hre,
      contractName: 'IssuerLogic',
    })
    const { contract: remigrationContract } = await getContractWithSigner<RemigrationRestore>({
      hre,
      contractName: 'RemigrationRestore',
    })

    console.log(`*** restore issuers data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'setIssuersAll' })
    const limit = 100

    while (issuers.length > limit) {
      const receipt = await remigrationContract.restoreIssuers(issuers.slice(0, limit), sigPrams.deadline, sigPrams.sig)
      await receipt
        .wait()
        .then((res) => {
          showEthersRes({ res })
        })
        .catch((error) => console.log(error))
      issuers = issuers.slice(limit)
    }

    if (issuers.length > 0) {
      const receipt = await remigrationContract.restoreIssuers(issuers, sigPrams.deadline, sigPrams.sig)
      await receipt
        .wait()
        .then((res) => {
          showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    }
  },
)
