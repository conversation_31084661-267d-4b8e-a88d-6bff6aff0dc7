import path from 'path'
import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { saveBackupToJson } from '@tasks/common/saveBackupToJson'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { IssuerLogic } from '@/types/contracts/IssuerLogic'
import { RemigrationBackup } from '@/types/contracts/remigration/RemigrationBackup'

wrappedTask('backupIssuers', 'backup all issuer data', { filePath: path.basename(__filename) }).setAction(
  async (_, hre) => {
    const { contract: issuerContract } = await getContractWithSigner<IssuerLogic>({ hre, contractName: 'IssuerLogic' })
    const { contract: remigrationContract } = await getContractWithSigner<RemigrationBackup>({
      hre,
      contractName: 'RemigrationBackup',
    })

    console.log(`*** backup issuers data...`)
    const sigPrams = await getBackupSignature({ hre, salt: 'getIssuersAll' })

    const issuers: Array<any> = []
    let offset = 0
    const limit = 100

    const totalCount = await issuerContract.getIssuerCount()
    console.log(`Total item: ${totalCount.toString()}`)

    while (issuers.length != Number(totalCount)) {
      if (issuers.length > Number(totalCount)) {
        console.error(`Error: Issuers count ${issuers.length} is greater than total count`)
        break
      }
      // [result, count, error]
      const [issuersTemp, , err] = await remigrationContract.backupIssuers(
        offset,
        limit,
        sigPrams.deadline,
        sigPrams.sig,
      )
      if (err != '') {
        console.log(`backup ${issuers.length + 1} ~ ${issuers.length + issuersTemp.length} failed`)
        console.log('Error:', err)
        break
      } else {
        console.log(`backup ${issuers.length + 1} ~ ${issuers.length + issuersTemp.length} items to local`)
        issuers.push(...issuersTemp)
      }
      offset += limit
    }
    console.log(`All ${totalCount} (equle to the total count) items have been successfully backed up.`)

    await saveBackupToJson({ data: issuers, fileName: 'issuers', networkName: hre.network.name })
  },
)
