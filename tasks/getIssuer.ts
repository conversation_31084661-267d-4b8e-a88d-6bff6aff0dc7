import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { IssuerLogic } from '@/types/contracts/IssuerLogic'

wrappedTask('getIssuer', 'Get issuer information', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'Issuer ID to retrieve')
  .setAction(async (taskArguments, hre) => {
    try {
      const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })

      console.log(`** getIssuer Parameters **\n`)
      const params = {
        issuerId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<IssuerLogic>({ hre, contractName: 'IssuerLogic' })

      const { name, bankCode, err } = await contract.getIssuer(issuerId)

      const issuerInfo = {
        name,
        bankCode,
        error: err,
      }

      console.log(`** Issuer Information **\n`)
      printTable({ data: issuerInfo })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
