import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { ProviderLogic } from '@/types/contracts/ProviderLogic'

wrappedTask('getTokenInfo', 'get Token', { filePath: path.basename(__filename) })
  .addParam('providerId', 'provider Id')
  .setAction(async (taskArguments, hre) => {
    try {
      const providerId = convertToHex({ hre, value: taskArguments.providerId || '' })

      console.log(`** getToken Parameters **\n`)
      printTable({
        data: {
          providerId,
        },
      })

      const { contract } = await getContractWithSigner<ProviderLogic>({ hre, contractName: 'ProviderLogic' })

      const { tokenId, name, symbol, totalSupply, enabled, err } = await contract.getToken(providerId)

      const tokenInfo = {
        tokenId,
        name,
        symbol,
        totalSupply,
        enabled,
        error: err,
      }

      console.log(`** Token Information **\n`)
      printTable({ data: tokenInfo })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
