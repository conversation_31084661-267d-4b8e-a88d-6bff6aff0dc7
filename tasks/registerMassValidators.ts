import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { getTime } from './common/tools'
import { ValidatorLogic } from '@/types/contracts/ValidatorLogic'

wrappedTask('registerMassValidators', 'register multiple validators', {
  filePath: path.basename(__filename),
})
  .addParam('numberOfValidators', 'number of validators to register')
  .addParam('validId', 'validator id')
  .addParam('issuerId', 'issuer id')
  .addParam('validName', 'validator name')
  .addParam('validKey', 'validator key')
  .setAction(async (taskArguments, hre) => {
    const numberOfValidators = Number(taskArguments.numberOfValidators || '1')

    const kmsSigner = kmsSignerProvider({ hre })

    const validatorName = taskArguments.validName || ''
    const validatorId = taskArguments.validId || ''
    const issuerId = taskArguments.issuerId || ''

    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<ValidatorLogic>({ hre, contractName: 'ValidatorLogic' })

    const transactionMap = new Map()

    console.log(`*** Start registering ${numberOfValidators} validators, this may take a while...`)
    for (let i = 0; i < numberOfValidators; i++) {
      // validatorId = 300, number = 1000
      // => pad value = 0001, 0002,...
      // => validatorId = 300001, 300002,...
      const padValue = i.toString().padStart(numberOfValidators.toString().length, '0')
      const validatorIdNumber = `${validatorId}${padValue}`
      const padIssuerId = convertToHex({ hre, value: `${issuerId}${padValue}` })
      const padValidatorId = convertToHex({ hre, value: `${validatorId}${padValue}` })
      const padValidatorName = convertToHex({ hre, value: `${validatorName}${padValue}` })

      const deadline = await getTime()
      const kmsSig = await kmsSigner.sign(
        ['bytes32', 'bytes32', 'bytes32', 'uint256'],
        [padValidatorId, padIssuerId, padValidatorName, deadline],
      )

      try {
        const tx = await contract.addValidator(padValidatorId, padIssuerId, padValidatorName, traceId, deadline, kmsSig)
        transactionMap.set(validatorIdNumber, tx.hash)
      } catch (e: any) {
        console.error(`Revert at callStatic for validator ${validatorIdNumber}`)
        console.error(`Reason: ${e?.error?.message || e?.message || JSON.stringify(e)}`)
        continue
      }

      await new Promise((resolve) => setTimeout(resolve, 2000))
    }

    await new Promise((resolve) => setTimeout(resolve, 20000))

    let registrationSuccessCount = 0
    for (const [validatorIdNumber, hash] of transactionMap) {
      const receipt = await hre.ethers.provider.getTransactionReceipt(hash)
      if (receipt && receipt.status === 1) {
        console.log(`Success to register validator ${validatorIdNumber}`)
        registrationSuccessCount++
      } else {
        console.log(`Failed to register validator ${validatorIdNumber}`)
      }
    }

    console.log(`Successfully registered ${registrationSuccessCount} validators`)
  })
