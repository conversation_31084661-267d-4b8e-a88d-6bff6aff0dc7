import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable } from './common/tools'

wrappedTask('getAccountAllList', 'Get all Account List information associated with ValidatorLogic.', {
  filePath: path.basename(__filename),
})
  .addParam('validId', 'validator id')
  .addParam('limit', 'Number of issuers to retrieve')
  .addParam('offset', 'Starting position of the list')
  .addOptionalParam('printFullAccountInfo', 'Print full account information (1: yes, 0: no)', '1')
  .setAction(async (taskArguments, hre) => {
    const { ethers } = hre

    const { validId = '', limit = 0, offset = 0, printFullAccountInfo = '1' } = { ...taskArguments }

    const params = {
      validId,
      limit,
      offset,
      printFullAccountInfo,
    }
    printTable({ data: params })

    const validatorId = convertToHex({ hre, value: validId })

    const { contract } = await getContractWithSigner({ hre, contractName: 'ValidatorLogic' })

    const receipt = await contract.getAccountAllList(validatorId, offset, limit)
    if (receipt.err) {
      console.log('Error getting account all list ', receipt.err)
    } else {
      console.log('*** Account All List ***')
      if (printFullAccountInfo === '1') {
        receipt.accounts.forEach((account, index) => {
          const accountInfo = {
            accountId: account.accountId,
            accountName: account.accountDataAll.accountName,
            accountStatus: account.accountDataAll.accountStatus,
            balance: account.accountDataAll.balance,
            reasonCode: account.accountDataAll.reasonCode,
            zoneId: account.accountDataAll.zoneId,
            zoneName: account.accountDataAll.zoneName,
            appliedAt: account.accountDataAll.appliedAt,
            registeredAt: account.accountDataAll.registeredAt,
            terminatingAt: account.accountDataAll.terminatingAt,
            terminatedAt: account.accountDataAll.terminatedAt,
            mintLimit: account.accountDataAll.mintLimit,
            burnLimit: account.accountDataAll.burnLimit,
            chargeLimit: account.accountDataAll.chargeLimit,
            dischargeLimit: account.accountDataAll.dischargeLimit,
            transferLimit: account.accountDataAll.transferLimit,
            cumulativeLimit: account.accountDataAll.cumulativeLimit,
            cumulativeAmount: account.accountDataAll.cumulativeAmount,
            cumulativeDate: account.accountDataAll.cumulativeDate,
            cumulativeMintLimit: account.accountDataAll.cumulativeTransactionLimits.cumulativeMintLimit,
            cumulativeMintAmount: account.accountDataAll.cumulativeTransactionLimits.cumulativeMintAmount,
            cumulativeBurnLimit: account.accountDataAll.cumulativeTransactionLimits.cumulativeBurnLimit,
            cumulativeBurnAmount: account.accountDataAll.cumulativeTransactionLimits.cumulativeBurnAmount,
            cumulativeChargeLimit: account.accountDataAll.cumulativeTransactionLimits.cumulativeChargeLimit,
            cumulativeChargeAmount: account.accountDataAll.cumulativeTransactionLimits.cumulativeChargeAmount,
            cumulativeDischargeLimit: account.accountDataAll.cumulativeTransactionLimits.cumulativeDischargeLimit,
            cumulativeDischargeAmount: account.accountDataAll.cumulativeTransactionLimits.cumulativeDischargeAmount,
            cumulativeTransferLimit: account.accountDataAll.cumulativeTransactionLimits.cumulativeTransferLimit,
            cumulativeTransferAmount: account.accountDataAll.cumulativeTransactionLimits.cumulativeTransferAmount,
          }

          console.log(`--- Account ${index + 1} ---`)
          printTable({ data: accountInfo })
        })
      }

      console.log(`Total Count: ${receipt.totalCount}, Elements Return: ${receipt.accounts.length}`)
    }
  })
