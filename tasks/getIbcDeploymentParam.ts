import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { AccessCtrl } from '@/types/contracts/AccessCtrl'
import { Account } from '@/types/contracts/Account'
import { BusinessZoneAccount } from '@/types/contracts/BusinessZoneAccount'
import { ContractManager } from '@/types/contracts/ContractManager'
import { FinancialCheck } from '@/types/contracts/FinancialCheck'
import { FinancialZoneAccount } from '@/types/contracts/FinancialZoneAccount'
import { IBCToken } from '@/types/contracts/IBCToken'
import { IssuerLogic } from '@/types/contracts/IssuerLogic'
import { ProviderLogic } from '@/types/contracts/ProviderLogic'
import { TokenLogic } from '@/types/contracts/TokenLogic'
import { ValidatorLogic } from '@/types/contracts/ValidatorLogic'

type ContractType = {
  TokenLogic: TokenLogic
  IBCToken: IBCToken
  ValidatorLogic: ValidatorLogic
  ProviderLogic: ProviderLogic
  Account: Account
  IssuerLogic: IssuerLogic
  BusinessZoneAccount: BusinessZoneAccount
  FinancialZoneAccount: FinancialZoneAccount
  FinancialCheck: FinancialCheck
  ContractManager: ContractManager
  AccessCtrl: AccessCtrl
}

const TARGET: (keyof ContractType)[] = [
  'TokenLogic',
  'IBCToken',
  'ValidatorLogic',
  'ProviderLogic',
  'Account',
  'IssuerLogic',
  'BusinessZoneAccount',
  'FinancialZoneAccount',
  'FinancialCheck',
  'ContractManager',
  'AccessCtrl',
] as const

wrappedTask('getIbcDeploymentParam', 'Get the address of the contract required for IBC deployment.', {
  filePath: path.basename(__filename),
}).setAction(async (_, hre) => {
  console.log('*** IBC Deployment Param 情報')
  for (let lp = 0; lp < TARGET.length; lp++) {
    const { contract, deployed } = await getContractWithSigner<ContractType[keyof ContractType]>({
      hre,
      contractName: TARGET[lp],
    })
    const version = await contract.version()
    console.log('%s\t%s\t%s', TARGET[lp], deployed.address, version)
  }
})
