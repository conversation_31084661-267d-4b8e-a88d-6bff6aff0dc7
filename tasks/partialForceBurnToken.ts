import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import * as Tools from './common/tools'
import * as PrivateKey from '@/privateKey'

wrappedTask('partialForceBurnToken', 'partial force burn token', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('burnedAmount', 'amount to burn')
  .addParam('burnedBalance', 'balance after burn')
  .addParam('issuerKey', 'issuer key')
  .setAction(async (args, hre) => {
    const issuerId = convertToHex({ hre, value: args.issuerId || '' })
    const accountId = convertToHex({ hre, value: args.accountId || '' })
    const burnedAmount = args.burnedAmount || ''
    const burnedBalance = args.burnedBalance || ''
    const traceId = convertToHex({ hre, value: 'traceId' })
    const issuerKey = args.issuerKey || ''

    const { contract } = await getContractWithSigner({ hre, contractName: 'IssuerLogic' })

    console.log(`*** Token partialForceBurn`)
    const deadline = await Tools.getTime()
    const sig = await PrivateKey.sig(
      issuerKey,
      ['bytes32', 'bytes32', 'uint256', 'uint256', 'uint256'],
      [issuerId, accountId, burnedAmount, burnedBalance, deadline],
    )

    const receipt = await contract.partialForceBurn(
      issuerId,
      accountId,
      burnedAmount,
      burnedBalance,
      traceId,
      deadline,
      sig[0],
    )
    await receipt
      .wait()
      .then((res) => {
        Tools.showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
