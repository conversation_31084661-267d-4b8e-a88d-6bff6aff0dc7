import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { executeReceipt } from './common/executeReceipt'
import { getTime } from './common/tools'
import * as PrivateKey from '@/privateKey'
import { IssuerLogic } from '@/types/contracts/IssuerLogic'

wrappedTask('cumulativeReset', 'reset daily cumulative amount', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('issuerKey', 'issuer key')
  .setAction(async (taskArguments, hre) => {
    const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const issuerKey = taskArguments.issuerKey || ''
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<IssuerLogic>({ hre, contractName: 'IssuerLogic' })

    console.log(`*** Issuer cumulative reset: ${accountId}`)
    const deadline = await getTime()
    const sig = await PrivateKey.sig(issuerKey, ['bytes32', 'bytes32', 'uint256'], [issuerId, accountId, deadline])

    await executeReceipt(contract.cumulativeReset(issuerId, accountId, traceId, deadline, sig[0]))
  })
