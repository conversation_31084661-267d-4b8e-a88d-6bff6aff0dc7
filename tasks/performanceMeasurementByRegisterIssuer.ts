import fs from 'fs'
import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { getTime, showEthersRes } from './common/tools'
import { IssuerLogic } from '@/types/contracts/IssuerLogic'

wrappedTask('performanceMeasurementByRegisterIssuer', 'performance measurement by registering issuer', {
  filePath: path.basename(__filename),
})
  .addParam('issuerId', 'issuer id')
  .addParam('bankCode', 'bank code')
  .addParam('issuerName', 'issuer name')
  .addParam('count', 'count')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const issuerId = taskArguments.issuerId || ''
    const issuerName = convertToHex({ hre, value: taskArguments.issuerName || '' })
    const bankCode = taskArguments.bankCode || ''
    const count = taskArguments.count || ''

    const result: any = []
    const txText: any = []

    const startTime = performance.now()
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<IssuerLogic>({ hre, contractName: 'IssuerLogic' })

    contract.connect(kmsSigner)

    const txs = [...Array(count).keys()].map(async (num) => {
      const currentBankCode = bankCode + num
      const currentIssuerId = convertToHex({ hre, value: issuerId + num })

      performance.mark('start' + num)
      const deadline = getTime()
      const kmsSig = await kmsSigner.sign(
        ['bytes32', 'uint16', 'string', 'uint256'],
        [currentIssuerId, currentBankCode, issuerName, deadline],
      )
      const receipt = await contract.addIssuer(currentIssuerId, currentBankCode, issuerName, traceId, deadline, kmsSig)
      await receipt.wait().then((res) => {
        performance.mark('end' + num)
        showEthersRes({ res })
        performance.measure(num.toString(), 'start' + num, 'end' + num)
        performance.getEntriesByName(num.toString())
        result.push(performance.getEntriesByName(num.toString())[0])
        txText.push(res)
      })
    })
    await Promise.all(txs)
      .then(() => console.log('promise-complete'))
      .catch((err) => console.log(err))
    const endTime = performance.now()
    fs.writeFileSync('data-issuer-' + issuerId + '.json', JSON.stringify(result))
    fs.writeFileSync('txs-issuer-' + issuerId + '.json', JSON.stringify(txText))
    console.log(endTime - startTime)
  })
